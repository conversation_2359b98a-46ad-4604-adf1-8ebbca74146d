import express from "express";
import settingsController from "../../controller/settings.controller";
import settingsValidator from "../../validators/settings.validator";

const router = express.Router();

// ============================================================================
// ESSENTIAL APIS - Only what's needed for Recipe Settings UI
// ============================================================================

/**
 * @swagger
 * components:
 *   schemas:
 *     RecipeSettings:
 *       type: object
 *       properties:
 *         privateRecipeVisibilitySettings:
 *           type: object
 *           properties:
 *             highlightChanges:
 *               type: boolean
 *         publicRecipeSettings:
 *           type: object
 *           properties:
 *             publicStoreAccess:
 *               type: boolean
 *         publicRecipeCallToAction:
 *           type: object
 *         recipeDetailsToDisplayPublicly:
 *           type: object
 */

/**
 * @swagger
 * /api/v1/private/settings/recipe-configuration:
 *   get:
 *     summary: Get recipe settings configuration
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Recipe settings configuration retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/RecipeSettings'
 *       500:
 *         description: Internal server error
 */
router.get("/recipe-configuration", settingsController.getRecipeConfiguration);

/**
 * @swagger
 * /api/v1/private/settings/recipe-configuration:
 *   put:
 *     summary: Update recipe settings configuration
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RecipeSettings'
 *     responses:
 *       200:
 *         description: Settings configuration updated successfully
 *       400:
 *         description: Invalid settings data
 *       500:
 *         description: Internal server error
 */
router.put(
  "/update-recipe-configuration",
  settingsValidator.recipeConfigurationValidator(),
  settingsController.updateRecipeConfiguration
);



export default router;
