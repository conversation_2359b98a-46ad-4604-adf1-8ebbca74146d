import express from "express";
import dashboardController from "../../controller/dashboard.controller";
import dashboardValidator from "../../validators/dashboard.validator";

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     DashboardOverview:
 *       type: object
 *       properties:
 *         totalRecipes:
 *           type: number
 *         totalIngredients:
 *           type: number
 *         totalCategories:
 *           type: number
 *         totalFoodAttributes:
 *           type: number
 *         totalRecipeMeasures:
 *           type: number
 *         totalPublicRecipes:
 *           type: number
 *         totalViews:
 *           type: number
 *         totalCtaClicks:
 *           type: number
 *         totalContactSubmissions:
 *           type: number
 *         topViewedRecipes:
 *           type: array
 *           items:
 *             type: object
 *         topClickedRecipes:
 *           type: array
 *           items:
 *             type: object
 *         recentActivity:
 *           type: array
 *           items:
 *             type: object
 *         quickActions:
 *           type: array
 *           items:
 *             type: object
 */

/**
 * @swagger
 * /v1/private/dashboard/overview:
 *   get:
 *     summary: Get dashboard overview statistics
 *     tags: [Dashboard]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: date_range
 *         schema:
 *           type: string
 *           enum: [last_7_days, last_30_days, last_90_days, last_year]
 *           default: last_30_days
 *         description: Date range for analytics data
 *       - in: query
 *         name: category_type
 *         schema:
 *           type: string
 *         description: Filter by category type/name
 *     responses:
 *       200:
 *         description: Dashboard overview retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/DashboardOverview'
 *       500:
 *         description: Internal server error
 */
// Professional single route definition using external validator
router.get(
  "/overview",
  dashboardValidator.getDashboardOverviewValidator(),
  dashboardController.getDashboardOverview
);

/**
 * @swagger
 * /v1/private/dashboard/export:
 *   get:
 *     summary: Export dashboard data
 *     tags: [Dashboard]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv]
 *           default: json
 *         description: Export format
 *       - in: query
 *         name: date_range
 *         schema:
 *           type: string
 *           enum: [last_7_days, last_30_days, last_90_days, last_year]
 *           default: last_30_days
 *         description: Date range for analytics data
 *     responses:
 *       200:
 *         description: Dashboard data exported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *           text/csv:
 *             schema:
 *               type: string
 *       500:
 *         description: Internal server error
 */
router.get(
  "/export",
  dashboardValidator.exportDashboardDataValidator(),
  dashboardController.exportDashboardData
);

/**
 * @swagger
 * /v1/private/dashboard/cta-analytics:
 *   get:
 *     summary: Get CTA click analytics for dashboard
 *     tags: [Dashboard]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: date_range
 *         schema:
 *           type: string
 *           enum: [last_7_days, last_30_days, last_90_days, last_year]
 *         description: Date range for analytics
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort order by clicks
 *       - in: query
 *         name: cta_type
 *         schema:
 *           type: string
 *           enum: [contact_info, contact_form, custom_cta]
 *         description: Filter by CTA type
 *       - in: query
 *         name: recipe_name
 *         schema:
 *           type: string
 *         description: Filter by recipe name (partial match)
 *     responses:
 *       200:
 *         description: CTA analytics for dashboard retrieved successfully
 */
router.get(
  "/cta-analytics",
  dashboardValidator.getCtaAnalyticsValidator(),
  dashboardController.getCtaAnalytics
);

/**
 * @swagger
 * /v1/private/dashboard/contact-analytics:
 *   get:
 *     summary: Get contact form submissions for dashboard
 *     tags: [Dashboard]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: date_range
 *         schema:
 *           type: string
 *           enum: [last_7_days, last_30_days, last_90_days, last_year]
 *         description: Date range for analytics
 *       - in: query
 *         name: recipe_id
 *         schema:
 *           type: integer
 *         description: Filter by specific recipe ID
 *       - in: query
 *         name: recipe_name
 *         schema:
 *           type: string
 *         description: Filter by recipe name (partial match)
 *       - in: query
 *         name: user_email
 *         schema:
 *           type: string
 *         description: Filter by user email (partial match)
 *     responses:
 *       200:
 *         description: Contact submissions for dashboard retrieved successfully
 */
router.get(
  "/contact-analytics",
  dashboardValidator.getContactAnalyticsValidator(),
  dashboardController.getContactAnalytics
);



export default router;
