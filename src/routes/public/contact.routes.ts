import express, { Router } from "express";
import contactController from "../../controller/contact.controller";
import contactValida<PERSON> from "../../validators/contact.validator";

const router: Router = express.Router();

// POST /api/v1/public/contact-us - Create new contact us submission
router.post(
  "/",
  contactValidator.createContactUsValidator(),
  contactController.createContactUs
);

// GET /api/v1/public/contact-us/list - Get all contact us submissions
router.get("/list", contactController.getAllContactUs);

// GET /api/v1/public/contact-us/get/:id - Get single contact us by ID
router.get(
  "/get/:id",
  contactValidator.getContactUsValidator(),
  contactController.getContactUsById
);

// PUT /api/v1/public/contact-us/update/:id - Update contact us by ID
router.put(
  "/update/:id",
  contactValidator.updateContactUsValidator(),
  contactController.updateContactUs
);

// DELETE /api/v1/public/contact-us/delete/:id - Delete contact us by ID
router.delete(
  "/delete/:id",
  contactValidator.deleteContactUsValidator(),
  contactController.deleteContactUs
);

export default router;
