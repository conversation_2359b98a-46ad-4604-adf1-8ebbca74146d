import express from "express";
import recipeController from "../../controller/recipe.controller";
import recipeValidator from "../../validators/recipe.validator";

const router = express.Router();

/**
 * @swagger
 * /public/recipes/list:
 *   get:
 *     tags:
 *       - Public Recipes
 *     summary: Get recipes list
 *     description: Ultra-optimized recipes list with millisecond response times. Shows public recipes by default, or private recipes when visibility=private.
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - name: search
 *         in: query
 *         description: Search in recipe title, public title, serve in, garnish
 *         required: false
 *         schema:
 *           type: string
 *       - name: categories
 *         in: query
 *         description: Filter by category IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "1,2,3"
 *       - name: allergens
 *         in: query
 *         description: Filter by allergen attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "4,5"
 *       - name: dietary
 *         in: query
 *         description: Filter by dietary attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "6,7"
 *       - name: cuisine
 *         in: query
 *         description: Filter by cuisine attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "8,9"
 *       - name: portion_cost_min
 *         in: query
 *         description: Minimum portion cost filter
 *         required: false
 *         schema:
 *           type: number
 *           minimum: 0
 *       - name: portion_cost_max
 *         in: query
 *         description: Maximum portion cost filter
 *         required: false
 *         schema:
 *           type: number
 *           minimum: 0
 *       - name: visibility
 *         in: query
 *         description: Recipe visibility filter (public by default, private to show private recipes)
 *         required: false
 *         schema:
 *           type: string
 *           enum: [public, private]
 *           default: public
 *       - name: sort_by
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [alphabetical, title, portion_cost, created_at, updated_at]
 *           default: updated_at
 *       - name: sort_order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *     responses:
 *       200:
 *         description: Public recipes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Public recipes fetched successfully"
 *                 count:
 *                   type: integer
 *                   example: 50
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       recipe_title:
 *                         type: string
 *                         example: "Grilled Chicken Breast"
 *                       recipe_public_title:
 *                         type: string
 *                         example: "Healthy Grilled Chicken"
 *                       description:
 *                         type: string
 *                         example: "Serve with vegetables"
 *                       recipe_status:
 *                         type: string
 *                         example: "publish"
 *                       has_recipe_public_visibility:
 *                         type: boolean
 *                         example: true
 *                       has_recipe_private_visibility:
 *                         type: boolean
 *                         example: true
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                       portion_cost:
 *                         type: number
 *                         example: 12.50
 *                       is_bookmarked:
 *                         type: boolean
 *                         example: false
 *                       main_image:
 *                         type: object
 *                         properties:
 *                           item_id:
 *                             type: integer
 *                           item_type:
 *                             type: string
 *                           item_link:
 *                             type: string
 *                       categories:
 *                         type: string
 *                         example: "Main Course, Healthy"
 *                       allergens:
 *                         type: string
 *                         example: "Dairy, Gluten"
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 size:
 *                   type: integer
 *                   example: 20
 *                 total_pages:
 *                   type: integer
 *                   example: 3
 *                 response_time_ms:
 *                   type: integer
 *                   example: 35
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.get(
  "/list",
  recipeValidator.getRecipesListValidator(),
  recipeController.getPublicRecipesList,
);

/**
 * @swagger
 * /public/recipes/get-by-id/{id}:
 *   get:
 *     tags:
 *       - Public Recipes
 *     summary: Get public recipe by ID or slug
 *     description: Retrieve a single published public recipe by its ID or slug with all relations
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID (numeric) or slug (string)
 *         required: true
 *         schema:
 *           oneOf:
 *             - type: integer
 *               example: 1
 *             - type: string
 *               example: "chicken-alfredo-pasta"
 *     responses:
 *       200:
 *         description: Public recipe retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Recipe'
 *       404:
 *         description: Recipe not found or not public
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/get-by-id/:id",
  recipeValidator.getRecipeValidator(),
  recipeController.getPublicRecipeById,
);

/**
 * @swagger
 * /public/recipes/impression/{id}:
 *   post:
 *     tags:
 *       - Public Recipes
 *     summary: Increment public recipe impression
 *     description: Increment the impression count for a public recipe by ID or slug (no authentication required)
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID (numeric) or slug (string)
 *         required: true
 *         schema:
 *           oneOf:
 *             - type: integer
 *               example: 1
 *             - type: string
 *               example: "chicken-alfredo-pasta"
 *     responses:
 *       200:
 *         description: Recipe impression updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe impression updated"
 *                 data:
 *                   type: object
 *                   properties:
 *                     recipe_id:
 *                       type: string
 *                       example: "1"
 *                     impression_count:
 *                       type: integer
 *                       example: 151
 *       404:
 *         description: Recipe not found or not public
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/impression/:id",
  recipeValidator.impressionValidator(),
  recipeController.incrementRecipeImpression,
);

export default router;
