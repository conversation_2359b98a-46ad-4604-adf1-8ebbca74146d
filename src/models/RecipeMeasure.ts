import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum MeasureStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeMeasureAttributes {
  id?: number;
  unit_title: string;
  unit_slug: string;
  unit_icon?: number;
  status: MeasureStatus;
  organization_id?: string;
  is_system_unit?: boolean;
  created_by: number;
  updated_by: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export class RecipeMeasure
  extends Model<RecipeMeasureAttributes, never>
  implements RecipeMeasureAttributes {
  id!: number;
  unit_title!: string;
  unit_slug!: string;
  unit_icon?: number;
  status!: MeasureStatus;
  organization_id?: string;
  is_system_unit?: boolean;
  created_by!: number;
  updated_by!: number;
  createdAt!: Date;
  updatedAt!: Date;
}

RecipeMeasure.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    unit_title: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    unit_slug: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    unit_icon: {
      type: DataTypes.INTEGER, // Store item_id reference
      allowNull: true,
      comment: "Foreign key reference to nv_items table for unit icon",
      references: {
        model: "nv_items",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    status: {
      type: DataTypes.ENUM(Object.values(MeasureStatus)),
      allowNull: false,
      defaultValue: MeasureStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    is_system_unit: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: "Whether this is a system default unit (created by Super Admin)",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_measure",
    modelName: "RecipeMeasure",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

// Define associations
RecipeMeasure.associate = (models: any) => {
  // RecipeMeasure belongs to User (created_by)
  RecipeMeasure.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeMeasure belongs to User (updated_by)
  RecipeMeasure.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  RecipeMeasure.belongsTo(models.Item, {
    foreignKey: "unit_icon",
    as: "iconItem",
    constraints: true, //
    onDelete: "SET NULL", //
    onUpdate: "CASCADE", //
  });

  // Note: Only nutrition-related unit_of_measure fields in RecipeAttributes and IngredientAttributes are text fields
  // Other measure fields like ingredient_measure in RecipeIngredients still reference this table

  // RecipeMeasure has many RecipeIngredients (ingredient_measure)
  RecipeMeasure.hasMany(models.RecipeIngredients, {
    foreignKey: "ingredient_measure",
    as: "recipeIngredients",
  });
};

export default RecipeMeasure;
