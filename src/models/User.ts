"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface userAttributes {
  id: number;
  keycloak_userId: string;
  organization_id: string;
  user_email: string;
  created_by: number;
  updated_by: number;
  createdAt: Date;
  updatedAt: Date;
}

export class User
  extends Model<userAttributes, never>
  implements userAttributes
{
  id!: number;
  keycloak_userId!: string;
  organization_id!: string;
  user_email!: string;
  created_by!: number;
  updated_by!: number;
  createdAt!: Date;
  updatedAt!: Date;
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    keycloak_userId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    user_email: {
      type: DataTypes.STRING,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    sequelize: sequelize,
    tableName: "users",
    modelName: "User",
  }
);
