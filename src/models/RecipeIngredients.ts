import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeIngredientsStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeIngredientsAttributes {
  recipe_id: number;
  ingredient_id: number;
  recipe_ingredient_status: RecipeIngredientsStatus;
  ingredient_quantity?: number;
  ingredient_measure?: number;
  ingredient_wastage?: number;
  ingredient_cost?: number;
  ingredient_cooking_method?: number;
  preparation_method?: number;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeIngredients
  extends Model<RecipeIngredientsAttributes, never>
  implements RecipeIngredientsAttributes {
  recipe_id!: number;
  ingredient_id!: number;
  recipe_ingredient_status!: RecipeIngredientsStatus;
  ingredient_quantity?: number;
  ingredient_measure?: number;
  ingredient_wastage?: number;
  ingredient_cost?: number;
  ingredient_cooking_method?: number;
  preparation_method?: number;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeIngredients.init(
  {
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    ingredient_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_ingredients",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    recipe_ingredient_status: {
      type: DataTypes.ENUM(Object.values(RecipeIngredientsStatus)),
      allowNull: false,
      defaultValue: RecipeIngredientsStatus.active,
    },
    ingredient_quantity: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    ingredient_measure: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "mo_recipe_measure",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    ingredient_wastage: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    ingredient_cost: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    ingredient_cooking_method: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "mo_food_attributes",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    preparation_method: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "mo_food_attributes",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_ingredients",
    modelName: "RecipeIngredients",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["recipe_id", "ingredient_id"],
        name: "unique_recipe_ingredient",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_ingredients_organization",
      },
      {
        fields: ["recipe_ingredient_status"],
        name: "idx_recipe_ingredients_status",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_ingredients_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_ingredients_updated_by",
      },
    ],
  }
);

// Define associations
RecipeIngredients.associate = (models: any) => {
  // RecipeIngredients belongs to Recipe
  RecipeIngredients.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeIngredients belongs to Ingredient
  RecipeIngredients.belongsTo(models.Ingredient, {
    foreignKey: "ingredient_id",
    as: "ingredient",
  });

  // RecipeIngredients belongs to RecipeMeasure (ingredient_measure)
  RecipeIngredients.belongsTo(models.RecipeMeasure, {
    foreignKey: "ingredient_measure",
    as: "measureUnit",
  });

  // RecipeIngredients belongs to FoodAttributes (ingredient_cooking_method)
  RecipeIngredients.belongsTo(models.FoodAttributes, {
    foreignKey: "ingredient_cooking_method",
    as: "cookingMethod",
  });

  // RecipeIngredients belongs to FoodAttributes (preparation_method)
  RecipeIngredients.belongsTo(models.FoodAttributes, {
    foreignKey: "preparation_method",
    as: "preparationMethod",
  });

  // RecipeIngredients belongs to User (created_by)
  RecipeIngredients.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeIngredients belongs to User (updated_by)
  RecipeIngredients.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });
};

export default RecipeIngredients;
