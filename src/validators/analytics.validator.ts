import { celebrate, Joi, Segments } from "celebrate";

// ============================================================================
// SIMPLIFIED ANALYTICS VALIDATORS - Only what's needed per feature document
// ============================================================================

// Valid CTA types from your requirements
const ctaTypes = [
  "contact_info", // Contact Info block
  "contact_form", // Contact Us form
  "custom_cta", // Custom CTA link
];

// Valid date range options for dashboard
const dateRangeOptions = [
  "today",
  "this_week",
  "this_month",
  "last_7_days",
  "last_month",
  "last_30_days",
  "last_90_days",
  "last_year",
  "custom", // For custom date range
];

// ============================================================================
// PUBLIC ANALYTICS VALIDATORS - Matching your feature requirements
// ============================================================================

/**
 * Validator for tracking recipe interactions on public recipes
 * Supports: recipe_view, recipe_bookmark, recipe_share
 * Essential for dashboard analytics requirements
 */
const trackRecipeViewValidator = () =>
  celebrate({
    [Segments.BODY]: {
      recipe_id: Joi.number().required(),
      organization_id: Joi.string().optional(),
      session_id: Joi.string().optional(),
      recipe_name: Joi.string().optional(),
      event_type: Joi.string()
        .valid("recipe_view", "recipe_bookmark", "recipe_share")
        .default("recipe_view")
        .optional(),
      // For recipe_view events
      view_duration: Joi.number().optional().min(0),
      referrer: Joi.string().optional(),
      // For recipe_share events
      share_platform: Joi.string()
        .valid("facebook", "twitter", "whatsapp", "email", "copy_link")
        .optional(),
    },
  });

/**
 * Validator for tracking CTA clicks on public recipes
 * Tracks clicks on Contact Info, Contact Form, Custom CTA buttons
 */
const trackCtaClickValidator = () =>
  celebrate({
    [Segments.BODY]: {
      recipe_id: Joi.number().required(),
      organization_id: Joi.string().optional(),
      session_id: Joi.string().optional(),
      recipe_name: Joi.string().optional(),
      cta_type: Joi.string()
        .valid(...ctaTypes)
        .required(),
      cta_text: Joi.string().optional(),
    },
  });

/**
 * Validator for contact form submissions from public recipes
 * Stores contact form data when users submit from public recipe pages
 */
const submitContactFormValidator = () =>
  celebrate({
    [Segments.BODY]: {
      recipe_id: Joi.number().required(),
      organization_id: Joi.string().optional(),
      recipe_name: Joi.string().optional(),
      name: Joi.string().required(),
      email: Joi.string().email().required(),
      mobile: Joi.string().optional(),
      message: Joi.string().required(),
    },
  });

// ============================================================================
// DASHBOARD ANALYTICS VALIDATORS - For your 50/50 dashboard layout
// ============================================================================

/**
 * Validator for getting CTA click analytics with pagination (no default values)
 * Shows: Recipe Name, CTA Type, Clicks, Last Clicked At
 */
const getCtaClickAnalyticsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .optional(),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
      sort: Joi.string().valid("asc", "desc").optional(),
      recipe_name: Joi.string().optional(), // Search filter
      cta_type: Joi.string()
        .valid(...ctaTypes)
        .optional()
        .description("Filter by CTA type"),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    },
  });

/**
 * Validator for getting contact form submissions with pagination (no default values)
 * Shows: Recipe Name, Name, Email, Mobile, Message, Submitted On
 */
const getContactSubmissionsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .optional(),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
      recipe_id: Joi.number().optional(), // Filter by recipe
      recipe_name: Joi.string()
        .optional()
        .description("Filter by recipe name (partial match)"),
      user_email: Joi.string()
        .email()
        .optional()
        .description("Filter by user email (partial match)"),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    },
  });

/**
 * Validator for deleting contact form submissions
 * Admin can delete individual submissions
 */
const deleteContactSubmissionValidator = () =>
  celebrate({
    [Segments.PARAMS]: {
      id: Joi.number().required(),
    },
  });

/**
 * Validator for analytics summary endpoint
 * Shows paginated analytics events with filtering options
 */
const getAnalyticsSummaryValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      page: Joi.number().min(1).default(1),
      limit: Joi.number().min(1).max(50).default(20),
      event_type: Joi.string()
        .valid("recipe_view", "cta_click", "contact_form_submit")
        .optional(),
      entity_type: Joi.string()
        .valid("recipe", "category", "ingredient")
        .optional(),
      entity_id: Joi.number().optional(),
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .default("last_30_days")
        .optional(),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
    },
  });

/**
 * Validator for recipe view analytics endpoint
 * Shows recipe view statistics with date filtering
 */
const getRecipeViewAnalyticsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .default("last_30_days"),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
      sort: Joi.string().valid("asc", "desc").default("desc"),
    },
  });

/**
 * Validator for getting recipe view statistics for private recipes
 * Validates recipe ID parameter
 */
const getRecipeViewStatisticsValidator = () =>
  celebrate({
    [Segments.PARAMS]: {
      recipeId: Joi.number().required().positive(),
    },
    [Segments.QUERY]: {
      organization_id: Joi.string().optional(),
    },
  });

/**
 * Validator for resetting recipe view statistics for private recipes
 * Validates recipe ID parameter and optional user_ids array in body
 */
const resetRecipeViewStatisticsValidator = () =>
  celebrate({
    [Segments.PARAMS]: {
      recipeId: Joi.string().required().pattern(/^\d+$/).messages({
        'string.pattern.base': 'Recipe ID must be a valid positive number',
        'any.required': 'Recipe ID is required'
      }),
    },
    [Segments.BODY]: {
      user_ids: Joi.array()
        .items(Joi.number().positive())
        .min(1)
        .optional()
        .messages({
          'array.min': 'user_ids array must contain at least one user ID when provided',
          'number.positive': 'All user IDs must be positive numbers',
        }),
    },
    [Segments.QUERY]: {
      organization_id: Joi.string().optional(),
    },
  });

export default {
  // Public tracking validators
  trackRecipeViewValidator,
  trackCtaClickValidator,
  submitContactFormValidator,

  // Dashboard analytics validators
  getCtaClickAnalyticsValidator,
  getContactSubmissionsValidator,
  deleteContactSubmissionValidator,
  getAnalyticsSummaryValidator,
  getRecipeViewAnalyticsValidator,

  // Recipe view statistics validators
  getRecipeViewStatisticsValidator,
  resetRecipeViewStatisticsValidator,
};
