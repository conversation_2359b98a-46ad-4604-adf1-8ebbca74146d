import { celebrate, Joi, Segments } from "celebrate";
import { MeasureStatus } from "../models/RecipeMeasure";

const createRecipeMeasureValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        unit_title: Joi.string().min(1).max(100).required(),
        unit_slug: Joi.string().max(100).optional(),
        status: Joi.string()
          .valid(...Object.values(MeasureStatus))
          .optional(),
        is_system_unit: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
      })
      .unknown(true), // Allow file uploads and other fields
  });

const updateRecipeMeasureValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        unit_title: Joi.string().min(1).max(100).optional(),
        unit_slug: Joi.string().max(100).optional(),
        status: Joi.string()
          .valid(...Object.values(MeasureStatus))
          .optional(),
        is_system_unit: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
      })
      .unknown(true), // Allow file uploads and other fields
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().min(1).required(),
    }),
  });

const deleteRecipeMeasureValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getRecipeMeasureValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getRecipeMeasuresListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      search: Joi.string().max(100).optional(),
      status: Joi.string()
        .valid(...Object.values(MeasureStatus))
        .optional(),
      sort_by: Joi.string()
        .valid("unit_title", "created_at", "updated_at")
        .default("created_at"),
      sort_order: Joi.string().valid("asc", "desc").default("desc"),
    }),
  });

// Default export object
export default {
  createRecipeMeasureValidator,
  updateRecipeMeasureValidator,
  deleteRecipeMeasureValidator,
  getRecipeMeasureValidator,
  getRecipeMeasuresListValidator,
};
