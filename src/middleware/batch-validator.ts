import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ValidationHelper } from '../helper/validation.helper';

/**
 * Validate recipe batch requests with appropriate schemas
 */
export const validateBatchRequest = (type: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const body = req.body;
      const errors: string[] = [];

      switch (type) {
        case 'basic-info':
          if (!body.recipe_title) {
            errors.push('Recipe title is required');
          }
          break;

        case 'ingredients-nutrition':
          if (!body.recipe_id) {
            errors.push('Recipe ID is required');
          } else if (isNaN(parseInt(body.recipe_id))) {
            errors.push('Recipe ID must be a number');
          }
          break;

        case 'steps':
          if (!body.recipe_id) {
            errors.push('Recipe ID is required');
          } else if (isNaN(parseInt(body.recipe_id))) {
            errors.push('Recipe ID must be a number');
          }

          if (!body.steps || !Array.isArray(body.steps)) {
            errors.push('Steps must be a valid array');
          } else if (body.steps.length === 0) {
            errors.push('At least one step is required');
          }

          if (!body.batch_number) {
            errors.push('Batch number is required');
          } else if (isNaN(parseInt(body.batch_number))) {
            errors.push('Batch number must be a number');
          }

          if (body.is_final_batch === undefined) {
            errors.push('is_final_batch flag is required');
          }
          break;

        case 'uploads':
          if (!body.recipe_id) {
            errors.push('Recipe ID is required');
          } else if (isNaN(parseInt(body.recipe_id))) {
            errors.push('Recipe ID must be a number');
          }

          if (!body.batch_number) {
            errors.push('Batch number is required');
          } else if (isNaN(parseInt(body.batch_number))) {
            errors.push('Batch number must be a number');
          }

          if (body.is_final_batch === undefined) {
            errors.push('is_final_batch flag is required');
          }
          break;

        default:
          break;
      }

      if (errors.length > 0) {
        res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: 'Validation failed',
          errors
        });
        return;
      }

      next();
    } catch (error) {
      res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: 'Invalid request format',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return;
    }
  };
};
