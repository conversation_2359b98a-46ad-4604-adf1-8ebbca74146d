import { db, sequelize } from "../models";

// Define transaction type based on sequelize instance
type SequelizeTransaction = Awaited<ReturnType<typeof sequelize.transaction>>;

/**
 * Enhanced transaction management utility
 * Provides safe transaction handling with proper cleanup
 */
export class TransactionManager {
  private transaction: SequelizeTransaction | null = null;
  private isCommitted = false;
  private isRolledBack = false;

  /**
   * Start a new transaction
   */
  async start(): Promise<SequelizeTransaction> {
    if (this.transaction) {
      throw new Error("Transaction already started");
    }
    
    this.transaction = await sequelize.transaction();
    this.isCommitted = false;
    this.isRolledBack = false;
    return this.transaction;
  }

  /**
   * Get the current transaction
   */
  get(): SequelizeTransaction {
    if (!this.transaction) {
      throw new Error("Transaction not started");
    }
    return this.transaction;
  }

  /**
   * Commit the transaction safely
   */
  async commit(): Promise<void> {
    if (!this.transaction) {
      throw new Error("No transaction to commit");
    }
    
    if (this.isCommitted || this.isRolledBack) {
      return; // Already handled
    }

    try {
      await this.transaction.commit();
      this.isCommitted = true;
    } catch (error) {
      console.error("Error committing transaction:", error);
      await this.safeRollback();
      throw error;
    }
  }

  /**
   * Rollback the transaction safely
   */
  async rollback(): Promise<void> {
    await this.safeRollback();
  }

  /**
   * Safe rollback that checks transaction state
   */
  private async safeRollback(): Promise<void> {
    if (!this.transaction) {
      return; // No transaction to rollback
    }

    if (this.isCommitted || this.isRolledBack) {
      return; // Already handled
    }

    try {
      // Check if transaction is still active
      if (!this.transaction.finished) {
        await this.transaction.rollback();
      }
      this.isRolledBack = true;
    } catch (error) {
      console.error("Error rolling back transaction:", error);
      // Don't throw here to avoid masking original error
    }
  }

  /**
   * Execute a function within a transaction with automatic cleanup
   */
  static async execute<T>(
    operation: (transaction: SequelizeTransaction) => Promise<T>
  ): Promise<T> {
    const manager = new TransactionManager();
    
    try {
      const transaction = await manager.start();
      const result = await operation(transaction);
      await manager.commit();
      return result;
    } catch (error) {
      await manager.rollback();
      throw error;
    }
  }

  /**
   * Check if transaction is active
   */
  isActive(): boolean {
    return this.transaction !== null && !this.isCommitted && !this.isRolledBack;
  }

  /**
   * Get transaction status
   */
  getStatus(): 'not_started' | 'active' | 'committed' | 'rolled_back' {
    if (!this.transaction) return 'not_started';
    if (this.isCommitted) return 'committed';
    if (this.isRolledBack) return 'rolled_back';
    return 'active';
  }
}

/**
 * File operation rollback utility
 * Tracks file operations for cleanup on transaction failure
 */
export class FileOperationTracker {
  private operations: Array<{
    type: 'move' | 'create' | 'delete';
    originalPath?: string;
    newPath?: string;
    itemId?: number;
  }> = [];

  /**
   * Track a file move operation
   */
  trackMove(originalPath: string, newPath: string, itemId?: number): void {
    this.operations.push({
      type: 'move',
      originalPath,
      newPath,
      itemId
    });
  }

  /**
   * Track a file creation
   */
  trackCreate(filePath: string, itemId?: number): void {
    this.operations.push({
      type: 'create',
      newPath: filePath,
      itemId
    });
  }

  /**
   * Track a file deletion
   */
  trackDelete(filePath: string): void {
    this.operations.push({
      type: 'delete',
      originalPath: filePath
    });
  }

  /**
   * Rollback all tracked file operations
   */
  async rollback(): Promise<void> {
    const uploadService = (await import("./upload.service")).default;
    const bucketName = process.env.NODE_ENV || "development";

    // Reverse the operations to undo them
    for (const operation of this.operations.reverse()) {
      try {
        switch (operation.type) {
          case 'move':
            if (operation.originalPath && operation.newPath) {
              // Move file back to original location using correct method
              await uploadService.moveFileInBucket(
                bucketName,
                operation.newPath,
                operation.originalPath,
                operation.itemId || 0
              );
            }
            break;

          case 'create':
            if (operation.newPath) {
              // Delete the created file using correct method
              await uploadService.deleteFileFromBucket(
                bucketName,
                operation.newPath
              );
            }
            break;

          case 'delete':
            // Can't restore deleted files, just log
            console.warn(`Cannot restore deleted file: ${operation.originalPath}`);
            break;
        }
      } catch (error) {
        console.error(`Error rolling back file operation:`, error);
        // Continue with other operations
      }
    }

    // Clear operations after rollback
    this.operations = [];
  }

  /**
   * Clear all tracked operations (call after successful commit)
   */
  clear(): void {
    this.operations = [];
  }
}

/**
 * Enhanced error handling utility
 */
export class ErrorHandler {
  /**
   * Handle database errors with specific error types
   */
  static handleDatabaseError(error: any): {
    statusCode: number;
    message: string;
    errorType: string;
  } {
    // Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return {
        statusCode: 400,
        message: error.errors?.[0]?.message || 'Validation error',
        errorType: 'VALIDATION_ERROR'
      };
    }

    // Unique constraint violations
    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors?.[0]?.path || 'field';
      return {
        statusCode: 409,
        message: `${field} already exists`,
        errorType: 'DUPLICATE_ERROR'
      };
    }

    // Foreign key constraint violations
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      return {
        statusCode: 400,
        message: 'Invalid reference to related data',
        errorType: 'FOREIGN_KEY_ERROR'
      };
    }

    // Database connection errors
    if (error.name === 'SequelizeConnectionError') {
      return {
        statusCode: 503,
        message: 'Database connection error',
        errorType: 'CONNECTION_ERROR'
      };
    }

    // Default error
    return {
      statusCode: 500,
      message: error.message || 'Internal server error',
      errorType: 'UNKNOWN_ERROR'
    };
  }

  /**
   * Create standardized error response
   */
  static createErrorResponse(
    error: any,
    res: any,
    defaultMessage: string = 'Something went wrong'
  ) {
    const { statusCode, message, errorType } = this.handleDatabaseError(error);
    
    return res.status(statusCode).json({
      status: false,
      message: res.__(message) || defaultMessage,
      errorType,
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
}
