
import { randomUUID } from 'crypto';

/**
 * Generate a URL-friendly slug from a title
 * @param title - The title to convert to slug
 * @param options - Configuration options
 * @returns Clean, URL-friendly slug
 */
export const generateSlug = (
  title: string,
  options: {
    maxLength?: number;
    separator?: string;
    lowercase?: boolean;
    removeSpecialChars?: boolean;
  } = {}
): string => {
  const {
    maxLength = 25,
    separator = "-",
    lowercase = true,
    removeSpecialChars = true,
  } = options;

  if (!title || typeof title !== "string") {
    throw new Error("Title is required and must be a string");
  }

  let slug = title.trim();

  // Convert to lowercase if specified
  if (lowercase) {
    slug = slug.toLowerCase();
  }

  // Remove special characters and replace with separator
  if (removeSpecialChars) {
    // Replace spaces and special characters with separator
    slug = slug
      .replace(/[^\w\s-]/g, "") // Remove special characters except word chars, spaces, and hyphens
      .replace(/[\s_-]+/g, separator) // Replace spaces, underscores, and multiple hyphens with single separator
      .replace(new RegExp(`^${separator}+|${separator}+$`, "g"), ""); // Remove leading/trailing separators
  }

  // Truncate to max length
  if (slug.length > maxLength) {
    slug = slug.substring(0, maxLength);
    // Ensure we don't cut off in the middle of a word
    const lastSeparatorIndex = slug.lastIndexOf(separator);
    if (lastSeparatorIndex > maxLength * 0.8) {
      slug = slug.substring(0, lastSeparatorIndex);
    }
  }

  // Remove trailing separator if any
  slug = slug.replace(new RegExp(`${separator}+$`), "");

  return slug;
};

/**
 * Generate a unique slug by checking against existing slugs
 * @param title - The title to convert to slug
 * @param checkExistence - Function to check if slug exists
 * @param options - Configuration options
 * @returns Unique slug
 */
export const generateUniqueSlug = async (
  title: string,
  checkExistence: (slug: string) => Promise<boolean>,
  options: {
    maxLength?: number;
    separator?: string;
    lowercase?: boolean;
    removeSpecialChars?: boolean;
    maxAttempts?: number;
  } = {}
): Promise<string> => {
  const { maxAttempts = 100, maxLength = 25, ...slugOptions } = options;

  // Reserve space for counter suffix (e.g., "-99")
  const reservedSpace = 4; // "-" + up to 3 digits
  const baseSlugMaxLength = Math.max(10, maxLength - reservedSpace);

  // Generate base slug with reduced max length to accommodate counter
  const baseSlug = generateSlug(title, {
    ...slugOptions,
    maxLength: baseSlugMaxLength
  });

  let slug = baseSlug;
  let counter = 1;

  // Check if base slug exists
  while ((await checkExistence(slug)) && counter <= maxAttempts) {
    const suffix = `-${counter}`;
    // Ensure the final slug doesn't exceed maxLength
    if (baseSlug.length + suffix.length > maxLength) {
      const truncatedBase = baseSlug.substring(0, maxLength - suffix.length);
      slug = `${truncatedBase}${suffix}`;
    } else {
      slug = `${baseSlug}${suffix}`;
    }
    counter++;
  }

  if (counter > maxAttempts) {
    // Fallback to UUID-based slug for guaranteed uniqueness
    const uuid = randomUUID().substring(0, 16); // Use first 8 chars of UUID
    const uuidSuffix = `-${uuid}`;
    if (baseSlug.length + uuidSuffix.length > maxLength) {
      const truncatedBase = baseSlug.substring(0, maxLength - uuidSuffix.length);
      slug = `${truncatedBase}${uuidSuffix}`;
    } else {
      slug = `${baseSlug}${uuidSuffix}`;
    }
  }

  return slug;
};

/**
 * Validate slug format
 * @param slug - The slug to validate
 * @returns Validation result
 */
export const validateSlug = (
  slug: string
): { valid: boolean; error?: string } => {
  if (!slug || typeof slug !== "string") {
    return { valid: false, error: "Slug is required and must be a string" };
  }

  // Check length
  if (slug.length < 2) {
    return { valid: false, error: "Slug must be at least 2 characters long" };
  }

  if (slug.length > 100) {
    return { valid: false, error: "Slug must be less than 100 characters" };
  }

  // Check format (only lowercase letters, numbers, and hyphens)
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  if (!slugRegex.test(slug)) {
    return {
      valid: false,
      error: "Slug can only contain lowercase letters, numbers, and hyphens",
    };
  }

  // Check for reserved words
  const reservedWords = [
    "admin",
    "api",
    "www",
    "mail",
    "ftp",
    "localhost",
    "test",
    "staging",
    "production",
    "dev",
    "development",
  ];

  if (reservedWords.includes(slug)) {
    return { valid: false, error: "Slug cannot be a reserved word" };
  }

  return { valid: true };
};

/**
 * Clean and normalize existing slug
 * @param slug - The slug to clean
 * @returns Cleaned slug
 */
export const cleanSlug = (slug: string): string => {
  if (!slug || typeof slug !== "string") {
    return "";
  }

  return slug
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "");
};

/**
 * Generate slug from multiple fields
 * @param fields - Array of field values to combine
 * @param options - Configuration options
 * @returns Combined slug
 */
export const generateCombinedSlug = (
  fields: string[],
  options: {
    maxLength?: number;
    separator?: string;
    fieldSeparator?: string;
  } = {}
): string => {
  const { fieldSeparator = "-", ...slugOptions } = options;

  const combinedTitle = fields
    .filter((field) => field && typeof field === "string")
    .map((field) => field.trim())
    .join(` ${fieldSeparator} `);

  return generateSlug(combinedTitle, slugOptions);
};

export default {
  generateSlug,
  generateUniqueSlug,
  validateSlug,
  cleanSlug,
  generateCombinedSlug,
};
