import { sequelize } from "../models";
import { Recipe } from "../models/Recipe";
import { Ingredient } from "../models/Ingreditant";

/**
 * Update ingredient cost timestamp when cost_per_unit changes
 */
export const updateIngredientCostTimestamp = async (
  ingredientId: number,
  transaction?: any
): Promise<void> => {
  try {
    await Ingredient.update(
      { cost_last_updated_at: new Date() },
      {
        where: { id: ingredientId },
        transaction,
      }
    );
  } catch (error) {
    console.error(`Error updating ingredient cost timestamp for ID ${ingredientId}:`, error);
    throw error;
  }
};

/**
 * Update ingredient nutrition timestamp when nutrition attributes change
 */
export const updateIngredientNutritionTimestamp = async (
  ingredientId: number,
  transaction?: any
): Promise<void> => {
  try {
    await Ingredient.update(
      { nutrition_last_updated_at: new Date() },
      {
        where: { id: ingredientId },
        transaction,
      }
    );
  } catch (error) {
    console.error(`Error updating ingredient nutrition timestamp for ID ${ingredientId}:`, error);
    throw error;
  }
};

/**
 * Update recipe cost calculation timestamp
 */
export const updateRecipeCostTimestamp = async (
  recipeId: number,
  transaction?: any
): Promise<void> => {
  try {
    await Recipe.update(
      { ingredient_costs_updated_at: new Date() },
      {
        where: { id: recipeId },
        transaction,
      }
    );
  } catch (error) {
    console.error(`Error updating recipe cost timestamp for ID ${recipeId}:`, error);
    throw error;
  }
};

/**
 * Update recipe nutrition calculation timestamp
 */
export const updateRecipeNutritionTimestamp = async (
  recipeId: number,
  transaction?: any
): Promise<void> => {
  try {
    await Recipe.update(
      { nutrition_values_updated_at: new Date() },
      {
        where: { id: recipeId },
        transaction,
      }
    );
  } catch (error) {
    console.error(`Error updating recipe nutrition timestamp for ID ${recipeId}:`, error);
    throw error;
  }
};

/**
 * Check if recipe costs are outdated compared to ingredient costs
 */
export const areRecipeCostsOutdated = async (
  recipeId: number,
  organizationId: string
): Promise<{
  isOutdated: boolean;
  outdatedIngredients: Array<{
    id: number;
    name: string;
    lastUpdated: Date;
  }>;
}> => {
  try {
    const query = `
      SELECT 
        r.ingredient_costs_updated_at as recipe_cost_timestamp,
        i.id as ingredient_id,
        i.ingredient_name,
        i.cost_last_updated_at as ingredient_cost_timestamp
      FROM mo_recipe r
      JOIN mo_recipe_ingredients ri ON r.id = ri.recipe_id
      JOIN mo_ingredients i ON ri.ingredient_id = i.id
      WHERE r.id = :recipeId 
        AND r.organization_id = :organizationId
        AND ri.recipe_ingredient_status = 'active'
        AND i.ingredient_status = 'active'
    `;

    const results = await sequelize.query(query, {
      replacements: { recipeId, organizationId },
      type: sequelize.QueryTypes.SELECT,
    }) as any[];

    if (results.length === 0) {
      return { isOutdated: false, outdatedIngredients: [] };
    }

    const recipeCostTimestamp = results[0].recipe_cost_timestamp;
    const outdatedIngredients: Array<{
      id: number;
      name: string;
      lastUpdated: Date;
    }> = [];

    let isOutdated = false;

    for (const result of results) {
      const ingredientCostTimestamp = result.ingredient_cost_timestamp;

      // If ingredient cost was updated after recipe cost calculation
      if (ingredientCostTimestamp && recipeCostTimestamp &&
        new Date(ingredientCostTimestamp) > new Date(recipeCostTimestamp)) {
        isOutdated = true;
        outdatedIngredients.push({
          id: result.ingredient_id,
          name: result.ingredient_name,
          lastUpdated: new Date(ingredientCostTimestamp),
        });
      }
    }

    return { isOutdated, outdatedIngredients };
  } catch (error) {
    console.error(`Error checking recipe cost freshness for recipe ID ${recipeId}:`, error);
    throw error;
  }
};

/**
 * Check if recipe nutrition values are outdated compared to ingredient nutrition
 */
export const areRecipeNutritionValuesOutdated = async (
  recipeId: number,
  organizationId: string
): Promise<{
  isOutdated: boolean;
  outdatedIngredients: Array<{
    id: number;
    name: string;
    lastUpdated: Date;
  }>;
}> => {
  try {
    const query = `
      SELECT 
        r.nutrition_values_updated_at as recipe_nutrition_timestamp,
        i.id as ingredient_id,
        i.ingredient_name,
        i.nutrition_last_updated_at as ingredient_nutrition_timestamp
      FROM mo_recipe r
      JOIN mo_recipe_ingredients ri ON r.id = ri.recipe_id
      JOIN mo_ingredients i ON ri.ingredient_id = i.id
      WHERE r.id = :recipeId 
        AND r.organization_id = :organizationId
        AND ri.recipe_ingredient_status = 'active'
        AND i.ingredient_status = 'active'
    `;

    const results = await sequelize.query(query, {
      replacements: { recipeId, organizationId },
      type: sequelize.QueryTypes.SELECT,
    }) as any[];

    if (results.length === 0) {
      return { isOutdated: false, outdatedIngredients: [] };
    }

    const recipeNutritionTimestamp = results[0].recipe_nutrition_timestamp;
    const outdatedIngredients: Array<{
      id: number;
      name: string;
      lastUpdated: Date;
    }> = [];

    let isOutdated = false;

    for (const result of results) {
      const ingredientNutritionTimestamp = result.ingredient_nutrition_timestamp;

      // If ingredient nutrition was updated after recipe nutrition calculation
      if (ingredientNutritionTimestamp && recipeNutritionTimestamp &&
        new Date(ingredientNutritionTimestamp) > new Date(recipeNutritionTimestamp)) {
        isOutdated = true;
        outdatedIngredients.push({
          id: result.ingredient_id,
          name: result.ingredient_name,
          lastUpdated: new Date(ingredientNutritionTimestamp),
        });
      }
    }

    return { isOutdated, outdatedIngredients };
  } catch (error) {
    console.error(`Error checking recipe nutrition freshness for recipe ID ${recipeId}:`, error);
    throw error;
  }
};

/**
 * Get all recipes that use a specific ingredient (for bulk timestamp updates)
 */
export const getRecipesUsingIngredient = async (
  ingredientId: number,
  organizationId: string
): Promise<number[]> => {
  try {
    const query = `
      SELECT DISTINCT r.id
      FROM mo_recipe r
      JOIN mo_recipe_ingredients ri ON r.id = ri.recipe_id
      WHERE ri.ingredient_id = :ingredientId
        AND r.organization_id = :organizationId
        AND ri.recipe_ingredient_status = 'active'
    `;

    const results = await sequelize.query(query, {
      replacements: { ingredientId, organizationId },
      type: sequelize.QueryTypes.SELECT,
    }) as any[];

    return results.map((result: any) => result.id);
  } catch (error) {
    console.error(`Error getting recipes using ingredient ID ${ingredientId}:`, error);
    throw error;
  }
};
