// import rabbitMQ from "./rabbitmq";
// import { RABBITMQ_QUEUE } from "../helper/constant";

export const setupConsumers = async () => {
  try {
    console.log("Setting up RabbitMQ consumers...");

    // Add your consumer logic here
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.NOTIFICATION_SUCCESS);
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_SUCCESS);
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_FAILED);
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_UPGRADE_FAILED);
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PAYMENT_UPGRADE_SUCCESS);
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PUBLIC_PAYMENT_FAILED);
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.PLAN_PUBLIC_PAYMENT_SUCCESS);
    // await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.SUBSCRIPTION_EXPIRY);

    console.log("RabbitMQ consumers are set up successfully.");
  } catch (error) {
    console.error("Error setting up RabbitMQ consumers:", error);
  }
};
