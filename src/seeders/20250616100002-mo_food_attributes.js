const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if food attributes already exist
    const existingAttributes = await queryInterface.sequelize.query(
      `SELECT * FROM mo_food_attributes WHERE organization_id IS NULL AND is_system_attribute = true AND attribute_type IN ('allergen', 'dietary')`,
      { type: QueryTypes.SELECT }
    );

    if (existingAttributes.length === 0) {
      // Allergens (14 Major Allergens)
      const allergens = [
        {
          name: "Gluten",
          slug: "gluten",
          description: "A protein found in wheat, barley, rye, and other grains that can cause allergic reactions in sensitive individuals.",
          type: "allergen"
        },
        {
          name: "Crustaceans",
          slug: "crustaceans",
          description: "Shellfish including crab, lobster, shrimp, and prawns that are common allergens.",
          type: "allergen"
        },
        {
          name: "Eggs",
          slug: "eggs",
          description: "Chicken eggs and egg products that can cause allergic reactions, especially in children.",
          type: "allergen"
        },
        {
          name: "Fish",
          slug: "fish",
          description: "All types of fish including salmon, tuna, cod, and other seafood that can trigger allergies.",
          type: "allergen"
        },
        {
          name: "Peanuts",
          slug: "peanuts",
          description: "Ground nuts that are one of the most common and severe food allergens.",
          type: "allergen"
        },
        {
          name: "Soy",
          slug: "soy",
          description: "Soybeans and soy products including tofu, soy sauce, and soy milk.",
          type: "allergen"
        },
        {
          name: "Milk",
          slug: "milk",
          description: "Dairy products including cow's milk, cheese, butter, and other dairy-based ingredients.",
          type: "allergen"
        },
        {
          name: "Tree Nuts",
          slug: "tree-nuts",
          description: "Nuts that grow on trees including almonds, walnuts, cashews, pistachios, and hazelnuts.",
          type: "allergen"
        },
        {
          name: "Celery",
          slug: "celery",
          description: "Celery stalks, leaves, seeds, and celery-based seasonings that can cause allergic reactions.",
          type: "allergen"
        },
        {
          name: "Mustard",
          slug: "mustard",
          description: "Mustard seeds, mustard powder, and prepared mustard products.",
          type: "allergen"
        },
        {
          name: "Sesame",
          slug: "sesame",
          description: "Sesame seeds and sesame oil commonly used in cooking and baking.",
          type: "allergen"
        },
        {
          name: "Sulphites",
          slug: "sulphites",
          description: "Chemical preservatives used in wine, dried fruits, and processed foods that can trigger reactions.",
          type: "allergen"
        },
        {
          name: "Lupin",
          slug: "lupin",
          description: "Lupin beans and flour used in some baked goods and pasta products.",
          type: "allergen"
        },
        {
          name: "Molluscs",
          slug: "molluscs",
          description: "Soft-bodied shellfish including mussels, clams, oysters, and squid.",
          type: "allergen"
        }
      ];

      // Dietary Attributes
      const dietaryAttributes = [
        {
          name: "Vegan",
          slug: "vegan",
          description: "Plant-based diet that excludes all animal products including meat, dairy, eggs, and honey.",
          type: "dietary"
        },
        {
          name: "Vegetarian",
          slug: "vegetarian",
          description: "Diet that excludes meat and fish but may include dairy products and eggs.",
          type: "dietary"
        },
        {
          name: "Gluten-Free",
          slug: "gluten-free",
          description: "Diet that excludes gluten, a protein found in wheat, barley, rye, and other grains.",
          type: "dietary"
        },
        {
          name: "Halal",
          slug: "halal",
          description: "Food prepared according to Islamic dietary laws and regulations.",
          type: "dietary"
        },
        {
          name: "Kosher",
          slug: "kosher",
          description: "Food prepared according to Jewish dietary laws and regulations.",
          type: "dietary"
        },
        {
          name: "Dairy-Free",
          slug: "dairy-free",
          description: "Diet that excludes all dairy products including milk, cheese, butter, and yogurt.",
          type: "dietary"
        },
        {
          name: "Nut-Free",
          slug: "nut-free",
          description: "Diet that excludes all tree nuts and peanuts to prevent allergic reactions.",
          type: "dietary"
        },
        {
          name: "Low-Carb",
          slug: "low-carb",
          description: "Diet that restricts carbohydrate intake, typically focusing on proteins and fats.",
          type: "dietary"
        },
        {
          name: "Keto",
          slug: "keto",
          description: "Very low-carb, high-fat diet that puts the body into a metabolic state called ketosis.",
          type: "dietary"
        },
        {
          name: "Paleo",
          slug: "paleo",
          description: "Diet based on foods presumed to be available to Paleolithic humans, excluding processed foods.",
          type: "dietary"
        }
      ];

      // Combine all attributes
      const allAttributes = [...allergens, ...dietaryAttributes];

      // Prepare bulk insert data
      const attributeData = allAttributes.map(attr => ({
        attribute_title: attr.name,
        attribute_slug: attr.slug,
        attribute_description: attr.description,
        attribute_type: attr.type,
        attribute_icon: null,
        attribute_status: "active",
        organization_id: null,
        is_system_attribute: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }));

      await queryInterface.bulkInsert("mo_food_attributes", attributeData);
      console.log("✅ Food Attributes (Allergens & Dietary) seeded successfully");
    } else {
      console.log("⏭️  Food Attributes already exist, skipping...");
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_food_attributes", {
      organization_id: null,
      is_system_attribute: true,
      attribute_type: ["allergen", "dietary"]
    });
  }
};
