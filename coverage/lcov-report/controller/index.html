
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for controller</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> controller</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/3367</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/3265</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/373</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/3213</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="analytics.controller.ts"><a href="analytics.controller.ts.html">analytics.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="209" class="abs low">0/209</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="224" class="abs low">0/224</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="26" class="abs low">0/26</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="200" class="abs low">0/200</td>
	</tr>

<tr>
	<td class="file low" data-value="category.controller.ts"><a href="category.controller.ts.html">category.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="258" class="abs low">0/258</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="256" class="abs low">0/256</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="248" class="abs low">0/248</td>
	</tr>

<tr>
	<td class="file low" data-value="contact.controller.ts"><a href="contact.controller.ts.html">contact.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="133" class="abs low">0/133</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="106" class="abs low">0/106</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="118" class="abs low">0/118</td>
	</tr>

<tr>
	<td class="file low" data-value="dashboard.controller.ts"><a href="dashboard.controller.ts.html">dashboard.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="69" class="abs low">0/69</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="63" class="abs low">0/63</td>
	</tr>

<tr>
	<td class="file low" data-value="foodAttributes.controller.ts"><a href="foodAttributes.controller.ts.html">foodAttributes.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="250" class="abs low">0/250</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="288" class="abs low">0/288</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="21" class="abs low">0/21</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="244" class="abs low">0/244</td>
	</tr>

<tr>
	<td class="file low" data-value="ingredients.controller.ts"><a href="ingredients.controller.ts.html">ingredients.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="733" class="abs low">0/733</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="556" class="abs low">0/556</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="102" class="abs low">0/102</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="698" class="abs low">0/698</td>
	</tr>

<tr>
	<td class="file low" data-value="recipe-batch.controller.ts"><a href="recipe-batch.controller.ts.html">recipe-batch.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="202" class="abs low">0/202</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="207" class="abs low">0/207</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="185" class="abs low">0/185</td>
	</tr>

<tr>
	<td class="file low" data-value="recipe.controller.ts"><a href="recipe.controller.ts.html">recipe.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1240" class="abs low">0/1240</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1236" class="abs low">0/1236</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="135" class="abs low">0/135</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1194" class="abs low">0/1194</td>
	</tr>

<tr>
	<td class="file low" data-value="recipeMeasure.controller.ts"><a href="recipeMeasure.controller.ts.html">recipeMeasure.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="191" class="abs low">0/191</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="212" class="abs low">0/212</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="17" class="abs low">0/17</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="184" class="abs low">0/184</td>
	</tr>

<tr>
	<td class="file low" data-value="settings.controller.ts"><a href="settings.controller.ts.html">settings.controller.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="82" class="abs low">0/82</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="156" class="abs low">0/156</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="79" class="abs low">0/79</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-27T12:41:15.306Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    