{"info": {"name": "Recipe Assignments API", "description": "Test collection for the consolidated recipe assignment management API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1/private/recipes", "type": "string"}, {"key": "authToken", "value": "YOUR_TOKEN_HERE", "type": "string"}, {"key": "recipeId", "value": "40", "type": "string"}], "item": [{"name": "1. Assign Users to Recipe", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": {{recipeId}},\n  \"user_ids\": [182, 180, 178]\n}"}, "url": {"raw": "{{baseUrl}}/manage-assignments", "host": ["{{baseUrl}}"], "path": ["manage-assignments"]}, "description": "Assign multiple users to a recipe. Recipe must be published and private."}, "response": [{"name": "Success Response", "code": 200, "body": "{\n  \"status\": true,\n  \"message\": \"Recipe assignments managed successfully\",\n  \"data\": {\n    \"recipe_id\": 40,\n    \"assigned_users\": 3,\n    \"operation\": \"manage_assignments\"\n  }\n}"}]}, {"name": "2. Update Assignments (Single User)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": {{recipeId}},\n  \"user_ids\": [182]\n}"}, "url": {"raw": "{{baseUrl}}/manage-assignments", "host": ["{{baseUrl}}"], "path": ["manage-assignments"]}, "description": "Update assignments to only one user (replaces previous assignments)."}, "response": [{"name": "Success Response", "code": 200, "body": "{\n  \"status\": true,\n  \"message\": \"Recipe assignments managed successfully\",\n  \"data\": {\n    \"recipe_id\": 40,\n    \"assigned_users\": 1,\n    \"operation\": \"manage_assignments\"\n  }\n}"}]}, {"name": "3. Unassign All Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": {{recipeId}},\n  \"user_ids\": []\n}"}, "url": {"raw": "{{baseUrl}}/manage-assignments", "host": ["{{baseUrl}}"], "path": ["manage-assignments"]}, "description": "Unassign all users by passing empty array."}, "response": [{"name": "Success Response", "code": 200, "body": "{\n  \"status\": true,\n  \"message\": \"Recipe assignments managed successfully\",\n  \"data\": {\n    \"recipe_id\": 40,\n    \"assigned_users\": 0,\n    \"operation\": \"unassign_all\"\n  }\n}"}]}, {"name": "4. Get My Assigned Recipes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/assigned-to-me?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["assigned-to-me"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get recipes assigned to the current user."}}, {"name": "5. Test In<PERSON>id <PERSON> (Error)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 99999,\n  \"user_ids\": [182]\n}"}, "url": {"raw": "{{baseUrl}}/manage-assignments", "host": ["{{baseUrl}}"], "path": ["manage-assignments"]}, "description": "Test with non-existent recipe ID."}, "response": [{"name": "Recipe Not Found", "code": 404, "body": "{\n  \"status\": false,\n  \"message\": \"Reci<PERSON> not found\"\n}"}]}, {"name": "6. Test Validation Error", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [182]\n}"}, "url": {"raw": "{{baseUrl}}/manage-assignments", "host": ["{{baseUrl}}"], "path": ["manage-assignments"]}, "description": "Test validation error (missing recipe_id)."}, "response": [{"name": "Validation Error", "code": 400, "body": "{\n  \"status\": false,\n  \"message\": \"Recipe ID is required\"\n}"}]}]}