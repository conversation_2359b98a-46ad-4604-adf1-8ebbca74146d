#!/usr/bin/env ts-node

/**
 * Test Runner Script for Recipe Batch APIs
 * Runs comprehensive tests without database connection
 */

import { spawn } from 'child_process';
import * as path from 'path';

interface TestSuite {
  name: string;
  command: string;
  description: string;
}

const testSuites: TestSuite[] = [
  {
    name: 'Unit Tests - Recipe Batch Controller',
    command: 'npm run test:batch',
    description: 'Tests all 4 Recipe Batch API endpoints with mocked dependencies',
  },
  {
    name: 'Unit Tests - Batch Validator Middleware',
    command: 'npm run test:validator',
    description: 'Tests validation middleware for all batch request types',
  },
  {
    name: 'Unit Tests - Batch Helper Functions',
    command: 'npm run test:helper',
    description: 'Tests utility functions for batch processing',
  },
  {
    name: 'All Unit Tests',
    command: 'npm run test:unit',
    description: 'Runs all unit tests together',
  },
  {
    name: 'Coverage Report',
    command: 'npm run test:coverage',
    description: 'Generates test coverage report',
  },
];

function runCommand(command: string): Promise<{ success: boolean; output: string }> {
  return new Promise((resolve) => {
    console.log(`\n🚀 Running: ${command}`);
    console.log('─'.repeat(50));

    const [cmd, ...args] = command.split(' ');
    const child = spawn(cmd, args, {
      stdio: 'pipe',
      shell: true,
      cwd: path.resolve(__dirname, '..'),
    });

    let output = '';
    let errorOutput = '';

    child.stdout?.on('data', (data) => {
      const text = data.toString();
      output += text;
      process.stdout.write(text);
    });

    child.stderr?.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      process.stderr.write(text);
    });

    child.on('close', (code) => {
      const success = code === 0;
      const fullOutput = output + errorOutput;
      
      if (success) {
        console.log(`\n✅ Command completed successfully`);
      } else {
        console.log(`\n❌ Command failed with exit code ${code}`);
      }
      
      resolve({ success, output: fullOutput });
    });

    child.on('error', (error) => {
      console.error(`\n❌ Command failed with error: ${error.message}`);
      resolve({ success: false, output: error.message });
    });
  });
}

async function runTestSuite(suite: TestSuite): Promise<boolean> {
  console.log(`\n📋 ${suite.name}`);
  console.log(`📝 ${suite.description}`);
  
  const result = await runCommand(suite.command);
  return result.success;
}

async function runAllTests(): Promise<void> {
  console.log('🧪 Recipe Batch APIs - Test Runner');
  console.log('═'.repeat(60));
  console.log('Running comprehensive tests without database connection');
  console.log('All dependencies are mocked for isolated unit testing');
  console.log('═'.repeat(60));

  const results: { suite: TestSuite; success: boolean }[] = [];
  let totalTests = 0;
  let passedTests = 0;

  for (const suite of testSuites) {
    const success = await runTestSuite(suite);
    results.push({ suite, success });
    
    if (success) {
      passedTests++;
    }
    totalTests++;
  }

  // Print summary
  console.log('\n📊 TEST SUMMARY');
  console.log('═'.repeat(60));
  
  results.forEach(({ suite, success }) => {
    const status = success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${suite.name}`);
  });

  console.log('─'.repeat(60));
  console.log(`📈 Results: ${passedTests}/${totalTests} test suites passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Recipe Batch APIs are working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the output above.');
  }

  console.log('\n📋 Test Coverage:');
  console.log('• Recipe Batch Controller - All 4 APIs tested');
  console.log('• Batch Validator Middleware - All validation scenarios');
  console.log('• Batch Helper Functions - Utility functions');
  console.log('• Error Handling - Database failures, validation errors');
  console.log('• Authentication & Authorization - User permissions');
  console.log('• File Upload Processing - Mock file operations');
  
  console.log('\n🔧 APIs Tested:');
  console.log('1. POST /v1/private/recipes/batch/basic-info');
  console.log('2. POST /v1/private/recipes/batch/ingredients-nutrition');
  console.log('3. POST /v1/private/recipes/batch/steps');
  console.log('4. POST /v1/private/recipes/batch/uploads');

  process.exit(passedTests === totalTests ? 0 : 1);
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.length > 0) {
  const testName = args[0];
  const suite = testSuites.find(s => 
    s.name.toLowerCase().includes(testName.toLowerCase()) ||
    s.command.includes(testName)
  );

  if (suite) {
    console.log(`🎯 Running specific test: ${suite.name}`);
    runTestSuite(suite).then(success => {
      process.exit(success ? 0 : 1);
    });
  } else {
    console.log('❌ Test suite not found. Available options:');
    testSuites.forEach(s => console.log(`  • ${s.name}`));
    process.exit(1);
  }
} else {
  // Run all tests
  runAllTests();
}
