/**
 * Test script for resetRecipeViewStatistics API
 * Run this to debug your API issues
 */

const axios = require('axios');

// Configuration - Update these values
const CONFIG = {
  baseURL: 'http://localhost:3000', // Update with your server URL
  token: 'YOUR_AUTH_TOKEN_HERE',    // Update with valid auth token
  recipeId: 123,                    // Update with actual recipe ID
  userIds: [456, 789]               // Update with actual user IDs (optional)
};

/**
 * Test the resetRecipeViewStatistics API
 */
async function testResetRecipeStats() {
  console.log('🧪 Testing resetRecipeViewStatistics API...\n');

  try {
    // Test 1: Reset all statistics
    console.log('📋 Test 1: Reset all statistics for recipe', CONFIG.recipeId);
    const response1 = await axios({
      method: 'DELETE',
      url: `${CONFIG.baseURL}/api/v1/private/analytics/reset-view-statistics/${CONFIG.recipeId}`,
      headers: {
        'Authorization': `Bearer ${CONFIG.token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Success - Reset all statistics:');
    console.log('Status:', response1.status);
    console.log('Response:', JSON.stringify(response1.data, null, 2));
    console.log('\n');

  } catch (error1) {
    console.log('❌ Error - Reset all statistics:');
    if (error1.response) {
      console.log('Status:', error1.response.status);
      console.log('Response:', JSON.stringify(error1.response.data, null, 2));
    } else {
      console.log('Error:', error1.message);
    }
    console.log('\n');
  }

  try {
    // Test 2: Reset statistics for specific users
    console.log('📋 Test 2: Reset statistics for specific users', CONFIG.userIds);
    const response2 = await axios({
      method: 'DELETE',
      url: `${CONFIG.baseURL}/api/v1/private/analytics/reset-view-statistics/${CONFIG.recipeId}`,
      headers: {
        'Authorization': `Bearer ${CONFIG.token}`,
        'Content-Type': 'application/json'
      },
      data: {
        user_ids: CONFIG.userIds
      }
    });

    console.log('✅ Success - Reset specific users:');
    console.log('Status:', response2.status);
    console.log('Response:', JSON.stringify(response2.data, null, 2));
    console.log('\n');

  } catch (error2) {
    console.log('❌ Error - Reset specific users:');
    if (error2.response) {
      console.log('Status:', error2.response.status);
      console.log('Response:', JSON.stringify(error2.response.data, null, 2));
    } else {
      console.log('Error:', error2.message);
    }
    console.log('\n');
  }

  // Test 3: Invalid recipe ID
  try {
    console.log('📋 Test 3: Invalid recipe ID (should fail)');
    const response3 = await axios({
      method: 'DELETE',
      url: `${CONFIG.baseURL}/api/v1/private/analytics/reset-view-statistics/invalid`,
      headers: {
        'Authorization': `Bearer ${CONFIG.token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('⚠️  Unexpected success with invalid recipe ID:');
    console.log('Response:', JSON.stringify(response3.data, null, 2));

  } catch (error3) {
    console.log('✅ Expected error with invalid recipe ID:');
    if (error3.response) {
      console.log('Status:', error3.response.status);
      console.log('Response:', JSON.stringify(error3.response.data, null, 2));
    } else {
      console.log('Error:', error3.message);
    }
    console.log('\n');
  }
}

/**
 * Test recipe requirements
 */
async function checkRecipeRequirements() {
  console.log('🔍 Checking recipe requirements...\n');
  
  // This would require database access - you can run these queries manually
  console.log('Run these SQL queries to check your recipe:');
  console.log('\n1. Check recipe exists and is private:');
  console.log(`
SELECT 
  r.id,
  r.recipe_title,
  r.has_recipe_private_visibility,
  r.recipe_status,
  r.organization_id,
  COUNT(ru.user_id) as assigned_users_count
FROM mo_recipe r
LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id
WHERE r.id = ${CONFIG.recipeId}
GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.recipe_status, r.organization_id;
  `);

  console.log('\n2. Check existing analytics data:');
  console.log(`
SELECT 
  ra.id,
  ra.event_type,
  ra.entity_id,
  ra.user_id,
  ra.organization_id,
  ra.created_at
FROM mo_recipe_analytics ra
WHERE ra.event_type = 'recipe_view'
  AND ra.entity_type = 'recipe'
  AND ra.entity_id = ${CONFIG.recipeId}
ORDER BY ra.created_at DESC
LIMIT 10;
  `);
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 Recipe Statistics Reset API Debugger\n');
  console.log('Configuration:');
  console.log('- Base URL:', CONFIG.baseURL);
  console.log('- Recipe ID:', CONFIG.recipeId);
  console.log('- User IDs:', CONFIG.userIds);
  console.log('- Token:', CONFIG.token ? 'Provided' : 'Missing');
  console.log('\n' + '='.repeat(50) + '\n');

  if (!CONFIG.token || CONFIG.token === 'YOUR_AUTH_TOKEN_HERE') {
    console.log('❌ Please update the CONFIG.token with a valid authentication token');
    return;
  }

  await checkRecipeRequirements();
  console.log('='.repeat(50) + '\n');
  await testResetRecipeStats();
  
  console.log('🏁 Testing completed!');
  console.log('\nIf you\'re still having issues:');
  console.log('1. Check server logs for detailed error messages');
  console.log('2. Verify the recipe meets all requirements (private, has assigned users)');
  console.log('3. Ensure your auth token has proper permissions');
  console.log('4. Check the database queries above to verify data exists');
}

// Run the tests
main().catch(console.error);
