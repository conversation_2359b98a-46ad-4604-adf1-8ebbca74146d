# Debug Guide: resetRecipeViewStatistics API

## Common Issues and Solutions

### 1. **Recipe Requirements Not Met**
The API has strict requirements that might be causing failures:

#### Requirements:
- Recipe must exist and not be deleted
- <PERSON><PERSON><PERSON> must have `has_recipe_private_visibility = true` (private recipe)
- <PERSON><PERSON><PERSON> must have assigned users in `mo_recipe_user` table
- User must have proper organization access

#### Debug Steps:
```sql
-- Check if recipe exists and meets requirements
SELECT 
  r.id,
  r.recipe_title,
  r.has_recipe_private_visibility,
  r.recipe_status,
  r.organization_id,
  COUNT(ru.user_id) as assigned_users_count
FROM mo_recipe r
LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id
WHERE r.id = YOUR_RECIPE_ID
GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.recipe_status, r.organization_id;
```

### 2. **Authentication/Authorization Issues**

#### Check User Access:
```javascript
// The API requires authenticated user with proper organization access
// Make sure your request includes:
{
  "headers": {
    "Authorization": "Bearer YOUR_TOKEN",
    "Content-Type": "application/json"
  }
}
```

### 3. **Request Format Issues**

#### Correct API Endpoint:
```
DELETE /api/v1/private/analytics/reset-view-statistics/{recipeId}
```

#### Request Examples:

**Reset all statistics for a recipe:**
```javascript
fetch('/api/v1/private/analytics/reset-view-statistics/123', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  }
})
```

**Reset statistics for specific users:**
```javascript
fetch('/api/v1/private/analytics/reset-view-statistics/123', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    user_ids: [456, 789, 101]
  })
})
```

### 4. **Validation Errors**

#### Common validation issues:
- `recipeId` must be a positive integer
- `user_ids` (if provided) must be an array of positive integers
- `user_ids` array cannot be empty if provided

### 5. **Database Issues**

#### Check if analytics data exists:
```sql
-- Check existing analytics data for the recipe
SELECT 
  ra.id,
  ra.event_type,
  ra.entity_id,
  ra.user_id,
  ra.organization_id,
  ra.created_at
FROM mo_recipe_analytics ra
WHERE ra.event_type = 'recipe_view'
  AND ra.entity_type = 'recipe'
  AND ra.entity_id = YOUR_RECIPE_ID
ORDER BY ra.created_at DESC;
```

## Debugging Steps

### Step 1: Enable Debug Logging
Add this to your analytics service to see what's happening:

```javascript
// In resetRecipeViewStatistics method, add more logging:
console.log("Debug - Recipe ID:", recipeId);
console.log("Debug - Organization ID:", organizationId);
console.log("Debug - User IDs:", userIds);
console.log("Debug - Recipe Result:", recipeResult);
```

### Step 2: Test with Postman/curl

```bash
# Test the API directly
curl -X DELETE \
  'http://localhost:YOUR_PORT/api/v1/private/analytics/reset-view-statistics/123' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_ids": [456, 789]
  }'
```

### Step 3: Check Server Logs
Look for these log messages:
- "recipeResult" - Shows if recipe was found
- "resetRecipeViewStatistics completed" - Shows execution time
- Any error messages in console

### Step 4: Verify Route Registration
Make sure the route is properly registered in your app:

```javascript
// Check if this exists in src/routes/private/index.ts
routes.use("/analytics", analyticsRoute);
```

## Expected Responses

### Success Response:
```json
{
  "status": true,
  "message": "Recipe view statistics reset successfully for all users"
}
```

### Error Responses:
```json
// Recipe not found
{
  "status": false,
  "message": "Recipe not found or access denied"
}

// Not a private recipe
{
  "status": false,
  "message": "Recipe view statistics reset is only available for private recipes"
}

// No assigned users
{
  "status": false,
  "message": "Recipe must have assigned users to reset statistics"
}

// Invalid recipe ID
{
  "status": false,
  "message": "Invalid recipe ID. Must be a positive number"
}
```

## Quick Fix Checklist

1. ✅ **Recipe exists and is private**
2. ✅ **Recipe has assigned users**
3. ✅ **Using correct HTTP method (DELETE)**
4. ✅ **Using correct endpoint path**
5. ✅ **Valid authentication token**
6. ✅ **Proper request format**
7. ✅ **Valid recipeId parameter**
8. ✅ **Valid user_ids array (if provided)**

## Test Recipe Setup

If you need to create a test scenario:

```sql
-- 1. Create/update a recipe to be private
UPDATE mo_recipe 
SET has_recipe_private_visibility = true 
WHERE id = YOUR_RECIPE_ID;

-- 2. Assign users to the recipe
INSERT INTO mo_recipe_user (recipe_id, user_id, created_at, updated_at)
VALUES 
  (YOUR_RECIPE_ID, USER_ID_1, NOW(), NOW()),
  (YOUR_RECIPE_ID, USER_ID_2, NOW(), NOW());

-- 3. Create some analytics data to reset
INSERT INTO mo_recipe_analytics (
  event_type, entity_type, entity_id, user_id, 
  organization_id, session_id, created_at, updated_at
)
VALUES 
  ('recipe_view', 'recipe', YOUR_RECIPE_ID, USER_ID_1, 'org123', 'session1', NOW(), NOW()),
  ('recipe_view', 'recipe', YOUR_RECIPE_ID, USER_ID_2, 'org123', 'session2', NOW(), NOW());
```
