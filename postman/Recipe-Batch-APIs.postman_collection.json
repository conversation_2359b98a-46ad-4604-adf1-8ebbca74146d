{"info": {"name": "Recipe Batch APIs", "description": "Comprehensive collection for Recipe Batch Processing APIs - Create recipes in batches with separate endpoints for basic info, ingredients/nutrition, steps, and file uploads", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8028", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "recipe_id", "value": "", "type": "string"}], "item": [{"name": "1. Create Recipe Basic Info", "event": [{"listen": "test", "script": {"exec": ["// Test response status", "pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "// Test response structure", "pm.test('Response has correct structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', true);", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('recipe_id');", "    pm.expect(jsonData.data).to.have.property('recipe_slug');", "});", "", "// Save recipe_id for subsequent requests", "if (pm.response.code === 201) {", "    const jsonData = pm.response.json();", "    pm.collectionVariables.set('recipe_id', jsonData.data.recipe_id);", "    console.log('Recipe ID saved:', jsonData.data.recipe_id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "platform-type", "value": "web"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_title\": \"Delicious Chicken Alfredo Pasta\",\n  \"recipe_public_title\": \"Creamy Chicken Alfredo\",\n  \"recipe_description\": \"A rich and creamy pasta dish with tender chicken and homemade alfredo sauce\",\n  \"recipe_preparation_time\": 15,\n  \"recipe_cook_time\": 25,\n  \"has_recipe_public_visibility\": true,\n  \"has_recipe_private_visibility\": false,\n  \"recipe_status\": \"draft\",\n  \"recipe_serve_in\": \"dinner\",\n  \"recipe_complexity_level\": \"medium\",\n  \"recipe_garnish\": \"Fresh parsley and parmesan cheese\",\n  \"recipe_head_chef_tips\": \"Don't overcook the chicken to keep it tender\",\n  \"recipe_foh_tips\": \"Serve immediately while hot for best taste\",\n  \"recipe_impression\": 5,\n  \"recipe_yield\": 4,\n  \"recipe_yield_unit\": \"servings\",\n  \"recipe_total_portions\": 4,\n  \"recipe_single_portion_size\": 350,\n  \"recipe_serving_method\": \"plated\",\n  \"recipe_placeholder\": \"chicken-alfredo-placeholder.jpg\",\n  \"is_ingredient_cooking_method\": true,\n  \"is_preparation_method\": true,\n  \"is_cost_manual\": false,\n  \"categories\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/v1/private/recipes/batch/basic-info", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "batch", "basic-info"]}, "description": "Creates the basic information for a recipe. This is the first step in the batch recipe creation process. Returns a recipe_id that should be used in subsequent batch requests."}}, {"name": "2. Add Ingredients, Nutrition & Cuisine", "event": [{"listen": "test", "script": {"exec": ["// Test response status", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Test response structure", "pm.test('Response has correct structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', true);", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('recipe_id');", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// Check if recipe_id is available", "const recipeId = pm.collectionVariables.get('recipe_id');", "if (!recipeId) {", "    console.log('Warning: recipe_id not found. Please run \"Create Recipe Basic Info\" first.');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "platform-type", "value": "web"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": {{recipe_id}},\n  \"ingredients\": [\n    {\n      \"id\": 1,\n      \"quantity\": 500,\n      \"measure\": 1,\n      \"wastage\": 5,\n      \"cost\": 12.50,\n      \"cooking_method\": 1,\n      \"preparation_method\": 1\n    },\n    {\n      \"id\": 2,\n      \"quantity\": 300,\n      \"measure\": 1,\n      \"wastage\": 2,\n      \"cost\": 8.75,\n      \"cooking_method\": 2,\n      \"preparation_method\": 2\n    },\n    {\n      \"id\": 3,\n      \"quantity\": 200,\n      \"measure\": 2,\n      \"wastage\": 1,\n      \"cost\": 15.25,\n      \"cooking_method\": 1,\n      \"preparation_method\": 1\n    }\n  ],\n  \"nutrition_attributes\": [\n    {\n      \"id\": 1,\n      \"unit_of_measure\": \"g\",\n      \"unit\": 45.5,\n      \"attribute_description\": \"Total protein content\",\n      \"use_default\": false\n    },\n    {\n      \"id\": 2,\n      \"unit_of_measure\": \"kcal\",\n      \"unit\": 650,\n      \"attribute_description\": \"Total calories\",\n      \"use_default\": false\n    }\n  ],\n  \"allergen_attributes\": {\n    \"contains\": [1, 2, 3],\n    \"may_contain\": [4, 5]\n  },\n  \"cuisine_attributes\": [1, 2],\n  \"dietary_attributes\": [1, 3],\n  \"haccp_attributes\": [\n    {\n      \"id\": 1,\n      \"attribute_description\": \"Cook to internal temperature of 165°F\",\n      \"use_default\": false\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/private/recipes/batch/ingredients-nutrition", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "batch", "ingredients-nutrition"]}, "description": "Adds ingredients, nutrition information, allergens, cuisine types, dietary attributes, and HACCP information to an existing recipe. Requires recipe_id from the basic info step."}}, {"name": "3. Add Recipe Steps (Batch 1)", "event": [{"listen": "test", "script": {"exec": ["// Test response status", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Test response structure", "pm.test('Response has correct structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', true);", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('recipe_id');", "    pm.expect(jsonData.data).to.have.property('batch_number');", "    pm.expect(jsonData.data).to.have.property('steps_in_batch');", "    pm.expect(jsonData.data).to.have.property('is_final_batch');", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// Check if recipe_id is available", "const recipeId = pm.collectionVariables.get('recipe_id');", "if (!recipeId) {", "    console.log('Warning: recipe_id not found. Please run previous steps first.');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "platform-type", "value": "web"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": {{recipe_id}},\n  \"batch_number\": 1,\n  \"is_final_batch\": false,\n  \"steps\": [\n    {\n      \"step_title\": \"Prepare Ingredients\",\n      \"step_description\": \"Gather all ingredients and measure them according to the recipe requirements\",\n      \"step_note\": \"Make sure chicken is at room temperature\",\n      \"step_warning\": \"Handle raw chicken with care to avoid cross-contamination\",\n      \"step_time\": 5,\n      \"step_temperature\": null,\n      \"step_temperature_unit\": null\n    },\n    {\n      \"step_title\": \"Cook Pasta\",\n      \"step_description\": \"Bring a large pot of salted water to boil and cook pasta according to package directions\",\n      \"step_note\": \"Reserve 1 cup of pasta water before draining\",\n      \"step_warning\": \"Be careful with boiling water\",\n      \"step_time\": 10,\n      \"step_temperature\": 100,\n      \"step_temperature_unit\": \"C\"\n    },\n    {\n      \"step_title\": \"Season Chicken\",\n      \"step_description\": \"Season chicken breasts with salt, pepper, and Italian herbs\",\n      \"step_note\": \"Let chicken rest for 5 minutes after seasoning\",\n      \"step_warning\": \"Wash hands thoroughly after handling raw chicken\",\n      \"step_time\": 3,\n      \"step_temperature\": null,\n      \"step_temperature_unit\": null\n    },\n    {\n      \"step_title\": \"Cook Chicken\",\n      \"step_description\": \"Heat oil in a large skillet and cook chicken until golden brown and cooked through\",\n      \"step_note\": \"Internal temperature should reach 165°F\",\n      \"step_warning\": \"Ensure chicken is fully cooked before proceeding\",\n      \"step_time\": 8,\n      \"step_temperature\": 165,\n      \"step_temperature_unit\": \"F\"\n    },\n    {\n      \"step_title\": \"Rest Chicken\",\n      \"step_description\": \"Remove chicken from heat and let it rest before slicing\",\n      \"step_note\": \"This helps retain juices\",\n      \"step_warning\": null,\n      \"step_time\": 5,\n      \"step_temperature\": null,\n      \"step_temperature_unit\": null\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/private/recipes/batch/steps", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "batch", "steps"]}, "description": "Adds recipe steps in batches. This is batch 1 with 5 steps. Set is_final_batch to false since there are more steps to add."}}, {"name": "4. Add Recipe Steps (Batch 2 - Final)", "event": [{"listen": "test", "script": {"exec": ["// Test response status", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Test response structure", "pm.test('Response has correct structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', true);", "    pm.expect(jsonData.data).to.have.property('is_final_batch', true);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "platform-type", "value": "web"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": {{recipe_id}},\n  \"batch_number\": 2,\n  \"is_final_batch\": true,\n  \"steps\": [\n    {\n      \"step_title\": \"Make Alfredo Sauce\",\n      \"step_description\": \"In the same skillet, melt butter and add minced garlic. Cook until fragrant, then add heavy cream\",\n      \"step_note\": \"Don't let the garlic burn\",\n      \"step_warning\": \"Keep heat at medium to prevent cream from curdling\",\n      \"step_time\": 5,\n      \"step_temperature\": null,\n      \"step_temperature_unit\": null\n    },\n    {\n      \"step_title\": \"Add Cheese\",\n      \"step_description\": \"Gradually whisk in parmesan cheese until sauce is smooth and creamy\",\n      \"step_note\": \"Add cheese slowly to prevent clumping\",\n      \"step_warning\": \"Remove from heat if sauce gets too thick\",\n      \"step_time\": 3,\n      \"step_temperature\": null,\n      \"step_temperature_unit\": null\n    },\n    {\n      \"step_title\": \"Combine and Serve\",\n      \"step_description\": \"Add cooked pasta and sliced chicken to the sauce. Toss to combine and serve immediately\",\n      \"step_note\": \"Use pasta water to thin sauce if needed\",\n      \"step_warning\": \"Serve immediately while hot\",\n      \"step_time\": 2,\n      \"step_temperature\": null,\n      \"step_temperature_unit\": null\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/private/recipes/batch/steps", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "batch", "steps"]}, "description": "Adds the final batch of recipe steps. This is batch 2 with the remaining 3 steps. Set is_final_batch to true to indicate this is the last batch of steps."}}, {"name": "5. Upload Recipe Files (Batch 1)", "event": [{"listen": "test", "script": {"exec": ["// Test response status", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Test response structure", "pm.test('Response has correct structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', true);", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('recipe_id');", "    pm.expect(jsonData.data).to.have.property('batch_number');", "    pm.expect(jsonData.data).to.have.property('files_uploaded');", "    pm.expect(jsonData.data).to.have.property('is_final_batch');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "platform-type", "value": "web"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_id", "value": "{{recipe_id}}", "type": "text"}, {"key": "batch_number", "value": "1", "type": "text"}, {"key": "is_final_batch", "value": "false", "type": "text"}, {"key": "files", "type": "file", "src": [], "description": "Upload up to 5 image/document files"}, {"key": "files", "type": "file", "src": [], "description": "Second file (optional)"}, {"key": "files", "type": "file", "src": [], "description": "Third file (optional)"}]}, "url": {"raw": "{{base_url}}/v1/private/recipes/batch/uploads", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "batch", "uploads"]}, "description": "Uploads recipe files in batches. This is batch 1. You can upload up to 5 files per batch. Supported formats: images (jpg, png, gif, webp, svg), documents (pdf, doc, docx, txt). Set is_final_batch to false if you have more files to upload."}}, {"name": "6. Upload Recipe Files (<PERSON><PERSON> 2 - Final)", "event": [{"listen": "test", "script": {"exec": ["// Test response status", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Test final batch completion", "pm.test('Final batch completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('is_final_batch', true);", "});", "", "// Recipe creation completed message", "pm.test('Recipe batch creation completed', function () {", "    console.log('🎉 Recipe batch creation process completed successfully!');", "    console.log('Recipe ID:', pm.collectionVariables.get('recipe_id'));", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "platform-type", "value": "web"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_id", "value": "{{recipe_id}}", "type": "text"}, {"key": "batch_number", "value": "2", "type": "text"}, {"key": "is_final_batch", "value": "true", "type": "text"}, {"key": "files", "type": "file", "src": [], "description": "Upload remaining files"}, {"key": "files", "type": "file", "src": [], "description": "Second file (optional)"}]}, "url": {"raw": "{{base_url}}/v1/private/recipes/batch/uploads", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "batch", "uploads"]}, "description": "Uploads the final batch of recipe files. This is batch 2 with remaining files. Set is_final_batch to true to indicate this is the last batch of file uploads."}}, {"name": "Utility - Get Recipe by ID", "event": [{"listen": "test", "script": {"exec": ["// Test response status", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Test recipe data structure", "pm.test('Recipe data is complete', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status', true);", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('recipe_title');", "    pm.expect(jsonData.data).to.have.property('recipe_slug');", "    pm.expect(jsonData.data).to.have.property('ingredients');", "    pm.expect(jsonData.data).to.have.property('steps');", "    pm.expect(jsonData.data).to.have.property('resources');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "platform-type", "value": "web"}], "url": {"raw": "{{base_url}}/v1/private/recipes/get-by-id/{{recipe_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "get-by-id", "{{recipe_id}}"]}, "description": "Utility endpoint to verify the created recipe with all batch data. Use this to confirm that all batch operations were successful."}}, {"name": "Utility - Get All Recipes", "request": {"method": "GET", "header": [{"key": "platform-type", "value": "web"}], "url": {"raw": "{{base_url}}/v1/private/recipes/list?page=1&limit=10&search=", "host": ["{{base_url}}"], "path": ["v1", "private", "recipes", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}]}, "description": "Utility endpoint to list all recipes and verify the batch-created recipe appears in the list."}}], "event": [{"listen": "prerequest", "script": {"exec": ["// Global pre-request script", "console.log('🚀 Starting Recipe Batch API request...');", "console.log('Base URL:', pm.collectionVariables.get('base_url'));", "console.log('Recipe ID:', pm.collectionVariables.get('recipe_id') || 'Not set yet');", "", "// Check authentication token", "const authToken = pm.collectionVariables.get('auth_token');", "if (!authToken || authToken === 'your_jwt_token_here') {", "    console.warn('⚠️  Please set your authentication token in collection variables');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Global test script", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Log response for debugging", "if (pm.response.code !== 200 && pm.response.code !== 201) {", "    console.error('❌ Request failed:', pm.response.status);", "    console.error('Response:', pm.response.text());", "} else {", "    console.log('✅ Request successful:', pm.response.status);", "}"], "type": "text/javascript"}}]}