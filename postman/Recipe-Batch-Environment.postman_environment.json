{"id": "recipe-batch-environment", "name": "Recipe Batch Environment", "values": [{"key": "base_url", "value": "http://localhost:8028", "description": "Base URL for the Recipe Microservice API", "enabled": true}, {"key": "auth_token", "value": "", "description": "JWT authentication token - Update this with your actual token", "enabled": true}, {"key": "recipe_id", "value": "", "description": "Recipe ID - Auto-populated after creating basic info", "enabled": true}, {"key": "organization_id", "value": "1", "description": "Organization ID for testing", "enabled": true}, {"key": "user_id", "value": "1", "description": "User ID for testing", "enabled": true}, {"key": "platform_type", "value": "web", "description": "Platform type header value", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}