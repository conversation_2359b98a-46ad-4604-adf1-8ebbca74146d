# Recipe Batch APIs - Postman Collection

This Postman collection provides comprehensive testing for the Recipe Batch Processing APIs, which allow creating recipes in separate, manageable batches.

## 📋 Overview

The Recipe Batch APIs are designed to handle large recipe creation processes by breaking them into smaller, focused steps:

1. **Basic Info** - Create recipe with essential information
2. **Ingredients & Nutrition** - Add ingredients, nutrition data, allergens, cuisine types
3. **Steps** - Add cooking steps in batches (5 steps per batch)
4. **File Uploads** - Upload recipe images and documents in batches (5 files per batch)

## 🚀 Quick Start

### Prerequisites

1. **Authentication Token**: You need a valid JWT token from the authentication system
2. **Server Running**: Ensure the Recipe Microservice is running on the configured port
3. **Database Access**: Ensure you have proper database permissions for recipe creation

### Setup Instructions

1. **Import Collection**: Import `Recipe-Batch-APIs.postman_collection.json` into Postman
2. **Set Variables**: Configure the collection variables:
   - `base_url`: Your server URL (default: `http://localhost:8028`)
   - `auth_token`: Your JWT authentication token
   - `recipe_id`: Will be auto-populated after creating basic info

3. **Set Authentication**: The collection uses Bearer Token authentication
   - Token is automatically applied from the `auth_token` variable
   - Update the token in collection variables when it expires

## 📝 API Endpoints

### 1. Create Recipe Basic Info
- **Method**: `POST`
- **Endpoint**: `/v1/private/recipes/batch/basic-info`
- **Purpose**: Creates the foundation recipe with basic information
- **Returns**: `recipe_id` for subsequent batch operations

**Key Fields**:
- `recipe_title` (required)
- `recipe_description`
- `recipe_preparation_time`, `recipe_cook_time`
- `recipe_yield`, `recipe_total_portions`
- `categories` (array of category IDs)

### 2. Add Ingredients, Nutrition & Cuisine
- **Method**: `POST`
- **Endpoint**: `/v1/private/recipes/batch/ingredients-nutrition`
- **Purpose**: Adds ingredients, nutrition data, allergens, and cuisine information
- **Requires**: `recipe_id` from step 1

**Key Sections**:
- `ingredients`: Array of ingredient objects with quantities and costs
- `nutrition_attributes`: Nutritional information
- `allergen_attributes`: Contains/may contain allergen data
- `cuisine_attributes`, `dietary_attributes`: Classification data
- `haccp_attributes`: Food safety information

### 3. Add Recipe Steps (Batches)
- **Method**: `POST`
- **Endpoint**: `/v1/private/recipes/batch/steps`
- **Purpose**: Adds cooking steps in batches of 5
- **Batch Processing**: Multiple requests with different `batch_number`

**Key Fields**:
- `batch_number`: Sequential batch number (1, 2, 3...)
- `is_final_batch`: `true` for the last batch, `false` otherwise
- `steps`: Array of step objects (max 5 per batch)

**Step Object Structure**:
```json
{
  "step_title": "Step name",
  "step_description": "Detailed instructions",
  "step_note": "Additional tips",
  "step_warning": "Safety warnings",
  "step_time": 10,
  "step_temperature": 165,
  "step_temperature_unit": "F"
}
```

### 4. Upload Recipe Files (Batches)
- **Method**: `POST`
- **Endpoint**: `/v1/private/recipes/batch/uploads`
- **Purpose**: Uploads recipe images and documents in batches
- **Content-Type**: `multipart/form-data`

**Form Fields**:
- `recipe_id`: Recipe ID from step 1
- `batch_number`: Sequential batch number
- `is_final_batch`: Boolean flag for last batch
- `files`: Multiple file uploads (max 5 per batch)

**Supported File Types**:
- **Images**: JPG, PNG, GIF, WebP, SVG
- **Documents**: PDF, DOC, DOCX, TXT
- **Max Size**: 10MB per file

## 🔧 Collection Features

### Automated Testing
Each request includes comprehensive tests:
- Response status validation
- Response structure verification
- Data integrity checks
- Error handling validation

### Variable Management
- **Auto-population**: `recipe_id` is automatically saved after basic info creation
- **Environment Support**: Easy switching between development/staging/production
- **Token Management**: Centralized authentication token handling

### Error Handling
- Detailed error logging in test results
- Response time monitoring
- Status code validation
- Helpful error messages for debugging

### Batch Progress Tracking
- Visual progress indicators in test results
- Batch completion validation
- Final batch confirmation
- Recipe creation completion notifications

## 📊 Usage Patterns

### Sequential Execution
Run requests in order for complete recipe creation:
1. Create Recipe Basic Info
2. Add Ingredients, Nutrition & Cuisine
3. Add Recipe Steps (Batch 1)
4. Add Recipe Steps (Batch 2 - Final)
5. Upload Recipe Files (Batch 1)
6. Upload Recipe Files (Batch 2 - Final)

### Batch Processing Examples

**Steps Batching**:
- 8 total steps = 2 batches (5 + 3)
- 12 total steps = 3 batches (5 + 5 + 2)
- Set `is_final_batch: true` only on the last batch

**File Upload Batching**:
- 7 files = 2 batches (5 + 2)
- 15 files = 3 batches (5 + 5 + 5)
- Each batch can have 1-5 files

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Errors (401)**
   - Check if `auth_token` is set correctly
   - Verify token hasn't expired
   - Ensure `platform-type: web` header is included

2. **Recipe Not Found (404)**
   - Verify `recipe_id` is set from basic info step
   - Check if recipe was created successfully
   - Ensure recipe belongs to your organization

3. **Validation Errors (400)**
   - Check required fields are provided
   - Verify data types match expected formats
   - Ensure batch numbers are sequential

4. **File Upload Issues**
   - Check file size limits (10MB max)
   - Verify supported file formats
   - Ensure proper form-data encoding

### Debug Tips

1. **Check Console Logs**: Test scripts provide detailed logging
2. **Verify Variables**: Use `{{recipe_id}}` preview to check values
3. **Test Individual Steps**: Run requests separately to isolate issues
4. **Check Response Bodies**: Review error messages for specific issues

## 🔍 Utility Endpoints

The collection includes utility endpoints for verification:

- **Get Recipe by ID**: Verify complete recipe data
- **Get All Recipes**: Check recipe appears in listings

## 📈 Performance Considerations

- **Batch Sizes**: Optimized for 5 items per batch (steps/files)
- **Response Times**: Monitored and tested (< 5 seconds expected)
- **File Uploads**: Processed asynchronously with rollback support
- **Database Transactions**: All operations are transactional

## 🔒 Security Features

- **JWT Authentication**: Required for all endpoints
- **Organization Isolation**: Recipes are organization-scoped
- **Role-Based Access**: Only authorized roles can create recipes
- **File Validation**: Strict file type and size validation
- **Input Sanitization**: All inputs are sanitized and validated

## 📚 Additional Resources

- **API Documentation**: Available at `/api-docs` endpoint
- **Error Codes**: Standard HTTP status codes with detailed messages
- **Rate Limiting**: Consider API rate limits for batch operations
- **Monitoring**: Check server logs for detailed operation tracking
