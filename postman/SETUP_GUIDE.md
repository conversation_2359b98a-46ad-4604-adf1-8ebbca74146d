# Recipe Batch APIs - Quick Setup Guide

## 🚀 Getting Started in 5 Minutes

### Step 1: Import Collection & Environment

1. **Import Collection**:
   - Open Postman
   - Click "Import" → "Upload Files"
   - Select `Recipe-Batch-APIs.postman_collection.json`

2. **Import Environment**:
   - Click "Import" → "Upload Files"
   - Select `Recipe-Batch-Environment.postman_environment.json`
   - Select the environment from the dropdown (top-right)

### Step 2: Configure Authentication

1. **Get JWT Token**:
   - Login to your application or use authentication endpoint
   - Copy the JWT token

2. **Set Token in Environment**:
   - Click the environment dropdown → "Edit"
   - Update `auth_token` with your JWT token
   - Save changes

### Step 3: Update Base URL (if needed)

- Default: `http://localhost:8028`
- Update `base_url` in environment if your server runs on different port

### Step 4: Test the Setup

1. **Run "Create Recipe Basic Info"**:
   - Should return status 201
   - `recipe_id` will be auto-saved for other requests

2. **Check Variables**:
   - Verify `recipe_id` is populated in environment variables

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | API server URL | `http://localhost:8028` |
| `auth_token` | JWT authentication token | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `recipe_id` | Auto-populated recipe ID | `123` |
| `organization_id` | Your organization ID | `1` |
| `platform_type` | Platform header value | `web` |

### Headers

All requests automatically include:
- `Authorization: Bearer {{auth_token}}`
- `platform-type: {{platform_type}}`
- `Content-Type: application/json` (for JSON requests)

## 📝 Usage Workflow

### Complete Recipe Creation Flow

1. **Create Recipe Basic Info** ✅
   - Creates recipe foundation
   - Returns `recipe_id`

2. **Add Ingredients, Nutrition & Cuisine** ✅
   - Adds ingredients with quantities
   - Adds nutrition information
   - Adds allergens and dietary info

3. **Add Recipe Steps (Batch 1)** ✅
   - First 5 steps
   - Set `is_final_batch: false`

4. **Add Recipe Steps (Batch 2 - Final)** ✅
   - Remaining steps
   - Set `is_final_batch: true`

5. **Upload Recipe Files (Batch 1)** ✅
   - First batch of files (up to 5)
   - Set `is_final_batch: false`

6. **Upload Recipe Files (Batch 2 - Final)** ✅
   - Remaining files
   - Set `is_final_batch: true`

### Verification Steps

- **Get Recipe by ID**: Verify complete recipe data
- **Get All Recipes**: Check recipe appears in listings

## 🛠️ Customization

### Using Sample Data

1. **Open `sample-data.json`**:
   - Contains realistic test data
   - Copy data for your requests

2. **Update IDs**:
   - Replace ingredient IDs with your database IDs
   - Replace category IDs with your database IDs
   - Update measure and attribute IDs

### Batch Size Customization

- **Steps**: Default 5 per batch (configurable)
- **Files**: Default 5 per batch (configurable)
- **Adjust batch numbers accordingly**

### File Upload Testing

1. **Prepare Test Files**:
   - Images: JPG, PNG, GIF, WebP, SVG
   - Documents: PDF, DOC, DOCX, TXT
   - Max 10MB per file

2. **Upload Process**:
   - Select files in form-data
   - Set proper batch numbers
   - Mark final batch correctly

## 🔍 Troubleshooting

### Common Issues & Solutions

1. **401 Unauthorized**:
   ```
   ❌ Issue: Invalid or expired token
   ✅ Solution: Update auth_token in environment
   ```

2. **404 Recipe Not Found**:
   ```
   ❌ Issue: recipe_id not set or invalid
   ✅ Solution: Run "Create Recipe Basic Info" first
   ```

3. **400 Validation Error**:
   ```
   ❌ Issue: Missing required fields
   ✅ Solution: Check request body against sample data
   ```

4. **File Upload Fails**:
   ```
   ❌ Issue: File too large or wrong format
   ✅ Solution: Check file size (<10MB) and format
   ```

### Debug Tips

1. **Check Console**: Test scripts log detailed information
2. **Verify Variables**: Use `{{variable}}` preview
3. **Check Response**: Review error messages in response body
4. **Test Individually**: Run requests one by one to isolate issues

## 📊 Testing Features

### Automated Tests

Each request includes:
- ✅ Status code validation
- ✅ Response structure checks
- ✅ Data integrity verification
- ✅ Performance monitoring

### Test Results

- **Green**: All tests passed
- **Red**: Tests failed (check details)
- **Console Logs**: Detailed debugging info

### Collection Runner

1. **Run Entire Collection**:
   - Click collection → "Run"
   - Select all requests
   - Run sequentially

2. **Monitor Progress**:
   - Watch test results
   - Check variable updates
   - Review response times

## 🎯 Best Practices

### Request Order

1. Always run "Create Recipe Basic Info" first
2. Run subsequent requests in sequence
3. Verify `recipe_id` is set before proceeding

### Batch Management

1. **Steps**: Plan your batches (5 steps each)
2. **Files**: Organize files by batch (5 files each)
3. **Final Batch**: Always mark the last batch correctly

### Error Handling

1. **Check Status**: Verify 200/201 responses
2. **Read Messages**: Review error messages carefully
3. **Fix Issues**: Address validation errors before proceeding

### Performance

1. **Response Times**: Monitor for performance issues
2. **File Sizes**: Keep files under 10MB
3. **Batch Sizes**: Don't exceed recommended limits

## 📞 Support

### Getting Help

1. **Check README.md**: Comprehensive documentation
2. **Review Sample Data**: Use provided examples
3. **Check API Docs**: Visit `/api-docs` endpoint
4. **Console Logs**: Review test script outputs

### Reporting Issues

Include:
- Request details
- Response body
- Console logs
- Environment variables (without sensitive data)

---

**Happy Testing! 🎉**

Your Recipe Batch APIs are now ready for comprehensive testing with this Postman collection.
