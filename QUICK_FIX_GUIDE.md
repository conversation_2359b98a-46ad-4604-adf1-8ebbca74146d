# Quick Fix Guide: resetRecipeViewStatistics Not Working

## 🚨 Most Common Issues

### 1. **Recipe is Not Private**
```sql
-- Check if recipe is private
SELECT id, recipe_title, has_recipe_private_visibility 
FROM mo_recipe 
WHERE id = YOUR_RECIPE_ID;

-- Fix: Make recipe private
UPDATE mo_recipe 
SET has_recipe_private_visibility = true 
WHERE id = YOUR_RECIPE_ID;
```

### 2. **Recipe Has No Assigned Users**
```sql
-- Check assigned users
SELECT recipe_id, user_id 
FROM mo_recipe_user 
WHERE recipe_id = YOUR_RECIPE_ID;

-- Fix: Assign users to recipe
INSERT INTO mo_recipe_user (recipe_id, user_id, created_at, updated_at)
VALUES (YOUR_RECIPE_ID, USER_ID, NOW(), NOW());
```

### 3. **No Analytics Data to Reset**
```sql
-- Check existing analytics data
SELECT COUNT(*) as analytics_count
FROM mo_recipe_analytics 
WHERE event_type = 'recipe_view' 
  AND entity_type = 'recipe' 
  AND entity_id = YOUR_RECIPE_ID;
```

### 4. **Wrong HTTP Method or URL**
```javascript
// ❌ Wrong
fetch('/api/v1/private/analytics/reset-view-statistics/123', {
  method: 'POST' // Should be DELETE
});

// ✅ Correct
fetch('/api/v1/private/analytics/reset-view-statistics/123', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  }
});
```

### 5. **Invalid Request Body**
```javascript
// ❌ Wrong
{
  "user_ids": "123,456" // Should be array, not string
}

// ✅ Correct
{
  "user_ids": [123, 456] // Array of numbers
}
```

## 🔍 Debug Steps

### Step 1: Check Server Logs
With the enhanced logging I added, you should see:
```
🔄 resetRecipeViewStatistics called
📝 Request params: { recipeId: '123' }
📝 Request body: { user_ids: [456, 789] }
👤 User: 1 org123
```

### Step 2: Test with Simple Request
```bash
curl -X DELETE \
  'http://localhost:3000/api/v1/private/analytics/reset-view-statistics/123' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json'
```

### Step 3: Check Database State
```sql
-- Complete recipe check
SELECT 
  r.id,
  r.recipe_title,
  r.has_recipe_private_visibility,
  r.recipe_status,
  r.organization_id,
  COUNT(ru.user_id) as assigned_users_count,
  COUNT(ra.id) as analytics_count
FROM mo_recipe r
LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id
LEFT JOIN mo_recipe_analytics ra ON r.id = ra.entity_id 
  AND ra.event_type = 'recipe_view' 
  AND ra.entity_type = 'recipe'
WHERE r.id = YOUR_RECIPE_ID
GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.recipe_status, r.organization_id;
```

## 🛠️ Quick Fixes

### Fix 1: Setup Test Recipe
```sql
-- Create a proper test recipe
UPDATE mo_recipe 
SET has_recipe_private_visibility = true,
    recipe_status = 'active'
WHERE id = 123;

-- Assign test users
INSERT INTO mo_recipe_user (recipe_id, user_id, created_at, updated_at)
VALUES 
  (123, 456, NOW(), NOW()),
  (123, 789, NOW(), NOW());

-- Create test analytics data
INSERT INTO mo_recipe_analytics (
  event_type, entity_type, entity_id, user_id, 
  organization_id, session_id, created_at, updated_at
)
VALUES 
  ('recipe_view', 'recipe', 123, 456, 'org123', 'session1', NOW(), NOW()),
  ('recipe_view', 'recipe', 123, 789, 'org123', 'session2', NOW(), NOW());
```

### Fix 2: Test API Response
```javascript
// Use this test function
async function testResetAPI() {
  try {
    const response = await fetch('/api/v1/private/analytics/reset-view-statistics/123', {
      method: 'DELETE',
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token'),
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', data);
    
    if (!response.ok) {
      console.error('API Error:', data.message);
    }
  } catch (error) {
    console.error('Network Error:', error);
  }
}
```

### Fix 3: Check Route Registration
Verify in `src/routes/private/index.ts`:
```javascript
import analyticsRoute from "./analytics.routes";
routes.use("/analytics", analyticsRoute);
```

## 📋 Expected Responses

### Success (All Users):
```json
{
  "status": true,
  "message": "Recipe view statistics reset successfully for all users",
  "data": {
    "recipe_id": 123,
    "recipe_title": "Test Recipe",
    "reset_type": "all users",
    "assigned_users_count": 2,
    "deleted_records": 5,
    "execution_time_ms": 45
  }
}
```

### Success (Specific Users):
```json
{
  "status": true,
  "message": "Recipe view statistics reset successfully for specified users",
  "data": {
    "recipe_id": 123,
    "recipe_title": "Test Recipe",
    "reset_type": "specific users",
    "affected_users": [456, 789],
    "deleted_records": 3,
    "execution_time_ms": 32
  }
}
```

### Common Errors:
```json
// Recipe not private
{
  "status": false,
  "message": "Recipe view statistics reset is only available for private recipes"
}

// No assigned users
{
  "status": false,
  "message": "Recipe must have assigned users to reset statistics"
}

// Recipe not found
{
  "status": false,
  "message": "Recipe not found or access denied"
}
```

## 🎯 Action Plan

1. **Run the SQL checks above** to verify recipe state
2. **Use the test script** I provided (`test-reset-recipe-stats.js`)
3. **Check server logs** for the debug messages
4. **Verify authentication** token is valid
5. **Test with simple curl** command first

## 🆘 Still Not Working?

If none of the above fixes work, the issue might be:

1. **Database connection issues**
2. **Sequelize model problems**
3. **Authentication middleware blocking requests**
4. **Route not properly registered**
5. **Environment-specific configuration**

Share the exact error message and server logs for more specific help!
