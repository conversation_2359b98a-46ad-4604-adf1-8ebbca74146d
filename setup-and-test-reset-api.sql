-- Setup and Test Script for resetRecipeViewStatistics API
-- Run these queries to create test data and verify the API works

-- ============================================
-- STEP 1: Create/Update Test Recipe
-- ============================================

-- Create a test recipe if it doesn't exist
INSERT IGNORE INTO mo_recipe (
  id, recipe_title, recipe_description, recipe_status, 
  has_recipe_private_visibility, organization_id, 
  created_by, updated_by, created_at, updated_at
) VALUES (
  999, 'Test Private Recipe', 'Test recipe for analytics reset', 'active',
  true, 'test-org-123', 1, 1, NOW(), NOW()
);

-- Or update existing recipe to be private
UPDATE mo_recipe 
SET has_recipe_private_visibility = true,
    recipe_status = 'active'
WHERE id = 999;

-- ============================================
-- STEP 2: Assign Test Users to Recipe
-- ============================================

-- Clean up existing assignments for test recipe
DELETE FROM mo_recipe_user WHERE recipe_id = 999;

-- Assign test users (replace with actual user IDs)
INSERT INTO mo_recipe_user (recipe_id, user_id, created_at, updated_at)
VALUES 
  (999, 1, NOW(), NOW()),
  (999, 2, NOW(), NOW()),
  (999, 3, NOW(), NOW());

-- ============================================
-- STEP 3: Create Test Analytics Data
-- ============================================

-- Clean up existing analytics for test recipe
DELETE FROM mo_recipe_analytics 
WHERE event_type = 'recipe_view' 
  AND entity_type = 'recipe' 
  AND entity_id = 999;

-- Create test analytics data
INSERT INTO mo_recipe_analytics (
  event_type, entity_type, entity_id, user_id, 
  organization_id, session_id, ip_address, user_agent,
  metadata, created_at, updated_at
)
VALUES 
  ('recipe_view', 'recipe', 999, 1, 'test-org-123', 'session1', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 1, 'test-org-123', 'session2', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 2, 'test-org-123', 'session3', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 2, 'test-org-123', 'session4', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 3, 'test-org-123', 'session5', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW());

-- ============================================
-- STEP 4: Verify Setup
-- ============================================

-- Check recipe setup
SELECT 
  r.id,
  r.recipe_title,
  r.has_recipe_private_visibility,
  r.recipe_status,
  r.organization_id,
  COUNT(ru.user_id) as assigned_users_count,
  COUNT(ra.id) as analytics_count
FROM mo_recipe r
LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id
LEFT JOIN mo_recipe_analytics ra ON r.id = ra.entity_id 
  AND ra.event_type = 'recipe_view' 
  AND ra.entity_type = 'recipe'
WHERE r.id = 999
GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.recipe_status, r.organization_id;

-- Expected result should show:
-- - has_recipe_private_visibility = 1 (true)
-- - assigned_users_count = 3
-- - analytics_count = 5

-- ============================================
-- STEP 5: Test Queries (What the API Runs)
-- ============================================

-- This is the query the API runs to check recipe
SELECT
  r.id,
  r.recipe_title,
  r.has_recipe_private_visibility,
  r.organization_id,
  COUNT(ru.user_id) as assigned_users_count
FROM mo_recipe r
LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id
WHERE r.id = 999
  AND r.recipe_status != 'deleted'
GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.organization_id;

-- This is the delete query for all users
SELECT COUNT(*) as records_to_delete
FROM mo_recipe_analytics ra
INNER JOIN mo_recipe_user ru ON ra.entity_id = ru.recipe_id
  AND ra.user_id = ru.user_id
WHERE ra.event_type = 'recipe_view'
  AND ra.entity_type = 'recipe'
  AND ra.entity_id = 999
  AND ra.user_id IS NOT NULL;

-- ============================================
-- STEP 6: Manual Test of Delete Operations
-- ============================================

-- Test delete for specific users (users 1 and 2)
-- This simulates what the API does
DELETE ra FROM mo_recipe_analytics ra
INNER JOIN mo_recipe_user ru ON ra.entity_id = ru.recipe_id
  AND ra.user_id = ru.user_id
WHERE ra.event_type = 'recipe_view'
  AND ra.entity_type = 'recipe'
  AND ra.entity_id = 999
  AND ra.user_id IN (1, 2);

-- Check remaining records
SELECT 
  ra.user_id,
  COUNT(*) as remaining_records
FROM mo_recipe_analytics ra
WHERE ra.event_type = 'recipe_view'
  AND ra.entity_type = 'recipe'
  AND ra.entity_id = 999
GROUP BY ra.user_id;

-- Should only show user_id = 3 with records

-- ============================================
-- STEP 7: Reset Test Data for API Testing
-- ============================================

-- Recreate all test analytics data for API testing
DELETE FROM mo_recipe_analytics 
WHERE event_type = 'recipe_view' 
  AND entity_type = 'recipe' 
  AND entity_id = 999;

INSERT INTO mo_recipe_analytics (
  event_type, entity_type, entity_id, user_id, 
  organization_id, session_id, ip_address, user_agent,
  metadata, created_at, updated_at
)
VALUES 
  ('recipe_view', 'recipe', 999, 1, 'test-org-123', 'session1', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 1, 'test-org-123', 'session2', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 2, 'test-org-123', 'session3', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 2, 'test-org-123', 'session4', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW()),
  ('recipe_view', 'recipe', 999, 3, 'test-org-123', 'session5', '127.0.0.1', 'Test Browser', '{}', NOW(), NOW());

-- ============================================
-- STEP 8: Verification Queries
-- ============================================

-- Final verification - run this after API test
SELECT 'Setup Complete - Ready for API Testing' as status;

SELECT 
  'Recipe ID: 999' as test_recipe,
  'Private: Yes' as privacy,
  'Assigned Users: 3' as users,
  'Analytics Records: 5' as analytics,
  'Ready for Testing' as status;

-- ============================================
-- API TEST COMMANDS
-- ============================================

/*
Now test the API with these curl commands:

1. Reset all statistics:
curl -X DELETE \
  'http://localhost:3000/api/v1/private/analytics/reset-view-statistics/999' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json'

2. Reset specific users:
curl -X DELETE \
  'http://localhost:3000/api/v1/private/analytics/reset-view-statistics/999' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"user_ids": [1, 2]}'

3. Test with invalid recipe ID (should fail):
curl -X DELETE \
  'http://localhost:3000/api/v1/private/analytics/reset-view-statistics/99999' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json'
*/
