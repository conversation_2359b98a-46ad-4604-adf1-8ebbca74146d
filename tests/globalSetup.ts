/**
 * Global setup for Jest tests
 */

export default async function globalSetup() {
  console.log('🚀 Setting up test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.NEXT_NODE_ENV = 'test';
  
  // Mock global configurations
  (global as any).config = {
    PORT: 8028,
    API_BASE_URL: 'http://localhost:8028/uploads',
    WEB_BASE_URL: 'http://localhost:3000',
    MINIO_ENDPOINT: 'http://localhost:9000',
    MINIO_ACCESS_KEY: 'test-access-key',
    MINIO_SECRET_KEY: 'test-secret-key',
    JWT_SECRET_KEY: 'test-jwt-secret',
    JWT_EXIPIRATION_TIME: '1h',
  };
  
  (global as any).db = {
    username: 'test',
    password: 'test',
    database: 'test_db',
    host: 'localhost',
    dialect: 'mysql',
  };
  
  console.log('✅ Test environment setup complete');
}
