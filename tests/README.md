# Recipe Batch APIs - Unit Testing

This directory contains comprehensive unit tests for the Recipe Batch APIs, designed to run **without database connection** using mocks and stubs.

## 🎯 Test Coverage

### APIs Tested
1. **Create Recipe Basic Info** - `POST /v1/private/recipes/batch/basic-info`
2. **Add Ingredients & Nutrition** - `POST /v1/private/recipes/batch/ingredients-nutrition`
3. **Add Recipe Steps** - `POST /v1/private/recipes/batch/steps`
4. **Upload Recipe Files** - `POST /v1/private/recipes/batch/uploads`

### Components Tested
- ✅ **Recipe Batch Controller** - All 4 API endpoints
- ✅ **Batch Validator Middleware** - Request validation
- ✅ **Batch Helper Functions** - Utility functions
- ✅ **Error Handling** - Database failures, validation errors
- ✅ **Authentication & Authorization** - User permissions
- ✅ **File Upload Processing** - Mock file operations

## 🚀 Quick Start

### Run All Tests
```bash
npm test
```

### Run Specific Test Suites
```bash
# Recipe Batch Controller tests
npm run test:batch

# Batch Validator Middleware tests
npm run test:validator

# Batch Helper Functions tests
npm run test:helper

# All unit tests
npm run test:unit

# Generate coverage report
npm run test:coverage
```

### Run Tests with Custom Script
```bash
# Run all tests with detailed output
ts-node scripts/run-tests.ts

# Run specific test suite
ts-node scripts/run-tests.ts batch
ts-node scripts/run-tests.ts validator
ts-node scripts/run-tests.ts helper
```

## 📁 Test Structure

```
tests/
├── setup.ts                           # Global test setup
├── globalSetup.ts                     # Jest global setup
├── globalTeardown.ts                  # Jest global teardown
├── unit/
│   ├── recipe-batch.controller.test.ts    # Controller tests
│   ├── batch-validator.middleware.test.ts # Middleware tests
│   └── batch.helper.test.ts               # Helper function tests
└── README.md                          # This file
```

## 🧪 Test Features

### Mocking Strategy
- **Database Models**: All Sequelize models are mocked
- **External Services**: Upload service, transaction manager mocked
- **Helper Functions**: Recipe history, slug generation mocked
- **Authentication**: User context mocked for different scenarios

### Test Scenarios

#### 1. Recipe Batch Controller Tests
- ✅ **Success Cases**: Valid data processing
- ✅ **Authentication**: Unauthorized access, permission denied
- ✅ **Validation**: Missing required fields, invalid data types
- ✅ **Database Errors**: Connection failures, constraint violations
- ✅ **Business Logic**: Batch processing, final batch handling
- ✅ **File Uploads**: Upload success/failure, file type validation

#### 2. Batch Validator Middleware Tests
- ✅ **Basic Info Validation**: Recipe title requirements
- ✅ **Ingredients Validation**: Recipe ID, data structure validation
- ✅ **Steps Validation**: Batch numbers, final batch flags
- ✅ **Uploads Validation**: Required fields, batch parameters
- ✅ **Error Handling**: Multiple validation errors, exception handling

#### 3. Batch Helper Function Tests
- ✅ **Batch Calculations**: Step batches, progress tracking
- ✅ **Array Splitting**: Batch size handling, edge cases
- ✅ **Progress Generation**: Percentage calculations, completion status
- ✅ **Integration Scenarios**: End-to-end batch processing

## 📊 Test Results

### Expected Output
```
🧪 Recipe Batch APIs - Test Runner
═══════════════════════════════════════════════════════════
Running comprehensive tests without database connection
All dependencies are mocked for isolated unit testing
═══════════════════════════════════════════════════════════

📋 Unit Tests - Recipe Batch Controller
📝 Tests all 4 Recipe Batch API endpoints with mocked dependencies
🚀 Running: npm run test:batch
──────────────────────────────────────────────────────────

 PASS  tests/unit/recipe-batch.controller.test.ts
  Recipe Batch Controller
    createRecipeBasicInfo
      ✓ should create recipe basic info successfully
      ✓ should return 401 when user is not authenticated
      ✓ should return 403 when user does not have permission
      ✓ should return 400 when recipe_title is missing
      ✓ should handle database errors gracefully
      ✓ should generate unique slug correctly
    addIngredientsNutritionCuisine
      ✓ should add ingredients, nutrition and cuisine data successfully
      ✓ should return 404 when recipe is not found
      ✓ should handle empty ingredients array
      ✓ should process allergen attributes correctly
    addRecipeSteps
      ✓ should add recipe steps successfully
      ✓ should handle final batch correctly
      ✓ should calculate step numbers correctly for different batches
      ✓ should return 400 when steps is not an array
      ✓ should not make existing steps inactive for non-first batch
    addRecipeUploads
      ✓ should upload recipe files successfully
      ✓ should handle final batch correctly
      ✓ should calculate resource order correctly for different batches
      ✓ should return 400 when no files are uploaded
      ✓ should handle upload service failure
      ✓ should determine file type correctly
      ✓ should not make existing resources inactive for non-first batch

Test Suites: 1 passed, 1 total
Tests:       21 passed, 21 total
```

## 🔧 Configuration

### Jest Configuration (`jest.config.js`)
- **Environment**: Node.js
- **TypeScript Support**: ts-jest preset
- **Coverage**: 70% threshold for branches, functions, lines, statements
- **Timeout**: 10 seconds per test
- **Mocking**: Auto-clear and restore mocks

### Test Setup (`tests/setup.ts`)
- **Global Mocks**: Config, database, console methods
- **Test Utilities**: Mock request/response creators
- **Date Mocking**: Consistent timestamps for testing

## 🛠️ Debugging Tests

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure TypeScript paths are configured
   # Check jest.config.js moduleNameMapping
   ```

2. **Mock Not Working**
   ```bash
   # Verify mock is called before import
   # Check mock implementation matches actual interface
   ```

3. **Async Test Failures**
   ```bash
   # Ensure all promises are awaited
   # Check test timeout configuration
   ```

### Debug Commands
```bash
# Run tests in watch mode
npm run test:watch

# Run with verbose output
npm test -- --verbose

# Run specific test file
npm test -- tests/unit/recipe-batch.controller.test.ts

# Debug specific test
npm test -- --testNamePattern="should create recipe basic info successfully"
```

## 📈 Coverage Reports

### Generate Coverage
```bash
npm run test:coverage
```

### Coverage Output
- **HTML Report**: `coverage/lcov-report/index.html`
- **JSON Report**: `coverage/coverage-final.json`
- **Text Summary**: Console output

### Coverage Targets
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## 🎯 Best Practices

### Writing Tests
1. **Arrange-Act-Assert**: Clear test structure
2. **Descriptive Names**: Test names explain the scenario
3. **Mock Isolation**: Each test has fresh mocks
4. **Edge Cases**: Test boundary conditions
5. **Error Scenarios**: Test failure paths

### Mock Strategy
1. **External Dependencies**: Always mock external services
2. **Database Operations**: Mock all database calls
3. **File System**: Mock file operations
4. **Network Calls**: Mock HTTP requests
5. **Time-Dependent**: Mock Date.now() for consistency

## 🚀 Running in CI/CD

### GitHub Actions Example
```yaml
- name: Run Recipe Batch API Tests
  run: |
    npm install
    npm run test:coverage
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage/lcov.info
```

### Docker Testing
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run test:coverage
```

## 📞 Support

### Getting Help
1. **Check Test Output**: Review detailed error messages
2. **Debug Mode**: Use `--verbose` flag for detailed output
3. **Mock Issues**: Verify mock implementations match interfaces
4. **Coverage Issues**: Check which lines are not covered

### Common Solutions
- **Module Not Found**: Check import paths and Jest configuration
- **Mock Not Called**: Ensure mock is set up before module import
- **Async Issues**: Verify all promises are properly awaited
- **Type Errors**: Check TypeScript configuration and types

---

**Happy Testing! 🎉**

These tests ensure your Recipe Batch APIs work correctly without requiring a database connection.
