/**
 * Jest setup file - runs before each test file
 */

// Mock global config
global.config = {
  PORT: 8028,
  API_BASE_URL: 'http://localhost:8028/uploads',
  WEB_BASE_URL: 'http://localhost:3000',
  MINIO_ENDPOINT: 'http://localhost:9000',
  MINIO_ACCESS_KEY: 'test-access-key',
  MINIO_SECRET_KEY: 'test-secret-key',
  JWT_SECRET_KEY: 'test-jwt-secret',
  JWT_EXIPIRATION_TIME: '1h',
};

// Mock global db config
global.db = {
  username: 'test',
  password: 'test',
  database: 'test_db',
  host: 'localhost',
  dialect: 'mysql',
};

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };

beforeEach(() => {
  // Reset console mocks before each test
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
  console.info = jest.fn();
});

afterEach(() => {
  // Restore console after each test
  Object.assign(console, originalConsole);
});

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.NEXT_NODE_ENV = 'test';

// Increase timeout for async operations
jest.setTimeout(10000);

// Mock Date.now for consistent testing
const mockDate = new Date('2024-01-01T00:00:00.000Z');
jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
Date.now = jest.fn(() => mockDate.getTime());

// Export test utilities
export const testUtils = {
  mockDate,
  createMockRequest: (overrides: any = {}) => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: {
      id: 1,
      organization_id: 1,
      roles: [{ role_name: 'admin' }],
    },
    ip: '127.0.0.1',
    get: jest.fn((header: string) => {
      const headers: any = {
        'User-Agent': 'test-agent',
        'platform-type': 'web',
      };
      return headers[header];
    }),
    ...overrides,
  }),
  
  createMockResponse: () => {
    const res: any = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      header: jest.fn().mockReturnThis(),
    };
    return res;
  },
  
  createMockNext: () => jest.fn(),
  
  createMockTransaction: () => ({
    commit: jest.fn().mockResolvedValue(undefined),
    rollback: jest.fn().mockResolvedValue(undefined),
  }),
  
  createMockModel: (data: any = {}) => ({
    id: 1,
    created_at: mockDate,
    updated_at: mockDate,
    ...data,
    save: jest.fn().mockResolvedValue(data),
    update: jest.fn().mockResolvedValue([1]),
    destroy: jest.fn().mockResolvedValue(1),
  }),
};
