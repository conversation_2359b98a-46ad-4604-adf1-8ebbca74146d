/**
 * Unit tests for Batch Validator Middleware
 */

import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { validateBatchRequest } from '../../src/middleware/batch-validator';
import { testUtils } from '../setup';

describe('Batch Validator Middleware', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = testUtils.createMockRequest();
    mockRes = testUtils.createMockResponse();
    mockNext = testUtils.createMockNext();
  });

  describe('basic-info validation', () => {
    const validator = validateBatchRequest('basic-info');

    it('should pass validation with valid basic-info data', () => {
      // Arrange
      mockReq.body = {
        recipe_title: 'Test Recipe',
        recipe_description: 'A test recipe',
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should fail validation when recipe_title is missing', () => {
      // Arrange
      mockReq.body = {
        recipe_description: 'A test recipe',
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['Recipe title is required'],
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should fail validation when recipe_title is empty string', () => {
      // Arrange
      mockReq.body = {
        recipe_title: '',
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['Recipe title is required'],
      });
    });
  });

  describe('ingredients-nutrition validation', () => {
    const validator = validateBatchRequest('ingredients-nutrition');

    it('should pass validation with valid ingredients-nutrition data', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 1,
        ingredients: [],
        nutrition_attributes: [],
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should fail validation when recipe_id is missing', () => {
      // Arrange
      mockReq.body = {
        ingredients: [],
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['Recipe ID is required'],
      });
    });

    it('should fail validation when recipe_id is not a number', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 'not-a-number',
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['Recipe ID must be a number'],
      });
    });
  });

  describe('steps validation', () => {
    const validator = validateBatchRequest('steps');

    it('should pass validation with valid steps data', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 1,
        steps: [
          { step_description: 'Step 1' },
          { step_description: 'Step 2' },
        ],
        batch_number: 1,
        is_final_batch: false,
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should fail validation when steps is not an array', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 1,
        steps: 'not an array',
        batch_number: 1,
        is_final_batch: false,
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['Steps must be a valid array'],
      });
    });

    it('should fail validation when steps array is empty', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 1,
        steps: [],
        batch_number: 1,
        is_final_batch: false,
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['At least one step is required'],
      });
    });

    it('should fail validation when batch_number is missing', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 1,
        steps: [{ step_description: 'Step 1' }],
        is_final_batch: false,
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['Batch number is required'],
      });
    });

    it('should fail validation when is_final_batch is undefined', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 1,
        steps: [{ step_description: 'Step 1' }],
        batch_number: 1,
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: ['is_final_batch flag is required'],
      });
    });

    it('should collect multiple validation errors', () => {
      // Arrange
      mockReq.body = {
        // Missing recipe_id, steps, batch_number, is_final_batch
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: [
          'Recipe ID is required',
          'Steps must be a valid array',
          'Batch number is required',
          'is_final_batch flag is required',
        ],
      });
    });
  });

  describe('uploads validation', () => {
    const validator = validateBatchRequest('uploads');

    it('should pass validation with valid uploads data', () => {
      // Arrange
      mockReq.body = {
        recipe_id: 1,
        batch_number: 1,
        is_final_batch: false,
      };

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should fail validation with missing required fields', () => {
      // Arrange
      mockReq.body = {};

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Validation failed',
        errors: [
          'Recipe ID is required',
          'Batch number is required',
          'is_final_batch flag is required',
        ],
      });
    });
  });

  describe('unknown validation type', () => {
    const validator = validateBatchRequest('unknown-type');

    it('should pass validation for unknown type', () => {
      // Arrange
      mockReq.body = {};

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    const validator = validateBatchRequest('basic-info');

    it('should handle exceptions gracefully', () => {
      // Arrange
      mockReq.body = null; // This will cause an error when accessing properties

      // Act
      validator(mockReq as Request, mockRes as Response, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Invalid request format',
        error: expect.any(String),
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
