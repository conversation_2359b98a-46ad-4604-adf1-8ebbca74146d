/**
 * Unit tests for Recipe Batch Controller
 * Tests all 4 batch APIs without database connection using mocks
 */

import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import recipeBatchController from '../../src/controller/recipe-batch.controller';
import { testUtils } from '../setup';

// Mock all dependencies
jest.mock('../../src/models', () => ({
  db: {
    Recipe: {
      findOne: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    RecipeAttributes: {
      update: jest.fn(),
      bulkCreate: jest.fn(),
    },
    RecipeIngredients: {
      update: jest.fn(),
      bulkCreate: jest.fn(),
    },
    RecipeSteps: {
      update: jest.fn(),
      bulkCreate: jest.fn(),
    },
    RecipeResources: {
      update: jest.fn(),
      bulkCreate: jest.fn(),
    },
    RecipeCategory: {
      bulkCreate: jest.fn(),
    },
  },
}));

jest.mock('../../src/helper/slugGenerator', () => ({
  generateUniqueSlug: jest.fn().mockResolvedValue('test-recipe-slug'),
}));

jest.mock('../../src/helper/recipe.helper', () => ({
  createRecipeHistory: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../../src/helper/timestamp.helper', () => ({
  updateRecipeCostTimestamp: jest.fn().mockResolvedValue(undefined),
  updateRecipeNutritionTimestamp: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../../src/helper/transaction.helper', () => ({
  TransactionManager: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockResolvedValue({}),
    commit: jest.fn().mockResolvedValue(undefined),
    rollback: jest.fn().mockResolvedValue(undefined),
  })),
  FileOperationTracker: jest.fn().mockImplementation(() => ({
    trackCreate: jest.fn(),
    clear: jest.fn(),
    rollback: jest.fn().mockResolvedValue(undefined),
  })),
  ErrorHandler: {
    createErrorResponse: jest.fn().mockImplementation((error, res, message) => {
      return res.status(500).json({
        status: false,
        message,
        error: error.message,
      });
    }),
  },
}));

jest.mock('../../src/helper/validation.helper', () => ({
  ValidationHelper: {
    sanitizeInput: jest.fn().mockImplementation((input) => input),
  },
}));

jest.mock('../../src/helper/upload.service', () => ({
  default: {
    uploadFileToBucket: jest.fn().mockResolvedValue({ success: true }),
  },
}));

// Import mocked modules
import { db } from '../../src/models';
import { generateUniqueSlug } from '../../src/helper/slugGenerator';
import { createRecipeHistory } from '../../src/helper/recipe.helper';
import { TransactionManager, FileOperationTracker } from '../../src/helper/transaction.helper';

describe('Recipe Batch Controller', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create fresh mock objects for each test
    mockReq = testUtils.createMockRequest();
    mockRes = testUtils.createMockResponse();
    mockNext = testUtils.createMockNext();
  });

  describe('createRecipeBasicInfo', () => {
    const validBasicInfoData = {
      recipe_title: 'Test Recipe',
      recipe_public_title: 'Public Test Recipe',
      recipe_description: 'A test recipe description',
      recipe_preparation_time: 15,
      recipe_cook_time: 30,
      has_recipe_public_visibility: true,
      has_recipe_private_visibility: false,
      recipe_status: 'draft',
      recipe_serve_in: 'dinner',
      recipe_complexity_level: 'medium',
      recipe_garnish: 'Fresh herbs',
      recipe_head_chef_tips: 'Cook with love',
      recipe_foh_tips: 'Serve hot',
      recipe_impression: 5,
      recipe_yield: 4,
      recipe_yield_unit: 'servings',
      recipe_total_portions: 4,
      recipe_single_portion_size: 250,
      recipe_serving_method: 'plated',
      recipe_placeholder: 'test-placeholder.jpg',
      is_ingredient_cooking_method: true,
      is_preparation_method: true,
      is_cost_manual: false,
      categories: [1, 2, 3],
    };

    it('should create recipe basic info successfully', async () => {
      // Arrange
      mockReq.body = validBasicInfoData;
      
      const mockRecipe = {
        id: 1,
        recipe_slug: 'test-recipe-slug',
        ...validBasicInfoData,
      };
      
      (db.Recipe.create as jest.Mock).mockResolvedValue(mockRecipe);
      (db.RecipeCategory.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.createRecipeBasicInfo(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.CREATED);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: true,
        message: 'Recipe basic information saved successfully',
        data: {
          recipe_id: 1,
          recipe_slug: 'test-recipe-slug',
        },
      });
      
      expect(db.Recipe.create).toHaveBeenCalledWith(
        expect.objectContaining({
          recipe_title: 'Test Recipe',
          recipe_slug: 'test-recipe-slug',
          organization_id: 1,
          created_by: 1,
          updated_by: 1,
        }),
        expect.objectContaining({ transaction: {} })
      );
      
      expect(db.RecipeCategory.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            recipe_id: 1,
            category_id: 1,
            status: 'active',
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
      
      expect(createRecipeHistory).toHaveBeenCalledWith(
        expect.objectContaining({
          recipe_id: 1,
          action: 'created',
          description: expect.stringContaining('Test Recipe'),
          organization_id: 1,
          created_by: 1,
        }),
        {}
      );
    });

    it('should return 401 when user is not authenticated', async () => {
      // Arrange
      mockReq.body = validBasicInfoData;
      mockReq.user = undefined;

      // Act
      await recipeBatchController.createRecipeBasicInfo(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.UNAUTHORIZED);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Unauthorized access',
      });
    });

    it('should return 403 when user does not have permission', async () => {
      // Arrange
      mockReq.body = validBasicInfoData;
      mockReq.user = {
        id: 1,
        organization_id: 1,
        roles: [{ role_name: 'customer' }], // Not an admin role
      };

      // Act
      await recipeBatchController.createRecipeBasicInfo(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.FORBIDDEN);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Permission denied',
      });
    });

    it('should return 400 when recipe_title is missing', async () => {
      // Arrange
      const invalidData = { ...validBasicInfoData };
      delete (invalidData as any).recipe_title;
      mockReq.body = invalidData;

      // Act
      await recipeBatchController.createRecipeBasicInfo(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Recipe title is required',
      });
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      mockReq.body = validBasicInfoData;
      const dbError = new Error('Database connection failed');
      (db.Recipe.create as jest.Mock).mockRejectedValue(dbError);

      // Act
      await recipeBatchController.createRecipeBasicInfo(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Error creating recipe basic information',
        error: 'Database connection failed',
      });
    });

    it('should generate unique slug correctly', async () => {
      // Arrange
      mockReq.body = validBasicInfoData;
      const mockRecipe = { id: 1, recipe_slug: 'unique-test-slug' };
      (db.Recipe.create as jest.Mock).mockResolvedValue(mockRecipe);
      (generateUniqueSlug as jest.Mock).mockResolvedValue('unique-test-slug');

      // Act
      await recipeBatchController.createRecipeBasicInfo(mockReq as Request, mockRes as Response);

      // Assert
      expect(generateUniqueSlug).toHaveBeenCalledWith(
        'Test Recipe',
        expect.any(Function),
        {
          maxLength: 25,
          separator: '-',
          lowercase: true,
        }
      );
    });
  });

  describe('addIngredientsNutritionCuisine', () => {
    const validIngredientsData = {
      recipe_id: 1,
      ingredients: [
        {
          id: 1,
          quantity: 500,
          measure: 1,
          wastage: 5,
          cost: 12.50,
          cooking_method: 1,
          preparation_method: 1,
        },
        {
          id: 2,
          quantity: 300,
          measure: 2,
          wastage: 2,
          cost: 8.75,
          cooking_method: 2,
          preparation_method: 2,
        },
      ],
      nutrition_attributes: [
        {
          id: 1,
          unit_of_measure: 'g',
          unit: 45.5,
          attribute_description: 'Total protein',
          use_default: false,
        },
      ],
      allergen_attributes: {
        contains: [1, 2, 3],
        may_contain: [4, 5],
      },
      cuisine_attributes: [1, 2],
      dietary_attributes: [1, 3],
      haccp_attributes: [
        {
          id: 1,
          attribute_description: 'Cook to 165°F',
          use_default: false,
        },
      ],
    };

    beforeEach(() => {
      const mockRecipe = {
        id: 1,
        recipe_title: 'Test Recipe',
        update: jest.fn().mockResolvedValue([1]),
      };
      (db.Recipe.findOne as jest.Mock).mockResolvedValue(mockRecipe);
    });

    it('should add ingredients, nutrition and cuisine data successfully', async () => {
      // Arrange
      mockReq.body = validIngredientsData;
      (db.RecipeIngredients.update as jest.Mock).mockResolvedValue([1]);
      (db.RecipeIngredients.bulkCreate as jest.Mock).mockResolvedValue([]);
      (db.RecipeAttributes.update as jest.Mock).mockResolvedValue([1]);
      (db.RecipeAttributes.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addIngredientsNutritionCuisine(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: true,
        message: 'Recipe ingredients, nutrition, and cuisine data saved successfully',
        data: { recipe_id: 1 },
      });

      // Verify ingredients were processed
      expect(db.RecipeIngredients.update).toHaveBeenCalledWith(
        { status: 'inactive' },
        expect.objectContaining({
          where: { recipe_id: 1 },
          transaction: {},
        })
      );

      expect(db.RecipeIngredients.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            recipe_id: 1,
            ingredient_id: 1,
            ingredient_quantity: 500,
            ingredient_measure: 1,
            ingredient_cost: 12.50,
            status: 'active',
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );

      // Verify attributes were processed
      expect(db.RecipeAttributes.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            recipe_id: 1,
            attribute_id: 1,
            attribute_type: 'nutrition',
            unit_of_measure: 'g',
            unit: 45.5,
          }),
          expect.objectContaining({
            recipe_id: 1,
            attribute_id: 1,
            attribute_type: 'allergen_contains',
          }),
          expect.objectContaining({
            recipe_id: 1,
            attribute_id: 1,
            attribute_type: 'cuisine',
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
    });

    it('should return 404 when recipe is not found', async () => {
      // Arrange
      mockReq.body = validIngredientsData;
      (db.Recipe.findOne as jest.Mock).mockResolvedValue(null);

      // Act
      await recipeBatchController.addIngredientsNutritionCuisine(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.NOT_FOUND);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Recipe not found',
      });
    });

    it('should handle empty ingredients array', async () => {
      // Arrange
      const dataWithoutIngredients = {
        ...validIngredientsData,
        ingredients: [],
      };
      mockReq.body = dataWithoutIngredients;

      // Act
      await recipeBatchController.addIngredientsNutritionCuisine(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.OK);
      expect(db.RecipeIngredients.update).not.toHaveBeenCalled();
      expect(db.RecipeIngredients.bulkCreate).not.toHaveBeenCalled();
    });

    it('should process allergen attributes correctly', async () => {
      // Arrange
      const dataWithAllergens = {
        recipe_id: 1,
        allergen_attributes: {
          contains: [1, 2],
          may_contain: [3, 4],
        },
      };
      mockReq.body = dataWithAllergens;
      (db.RecipeAttributes.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addIngredientsNutritionCuisine(mockReq as Request, mockRes as Response);

      // Assert
      expect(db.RecipeAttributes.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            attribute_type: 'allergen_contains',
            attribute_id: 1,
          }),
          expect.objectContaining({
            attribute_type: 'allergen_contains',
            attribute_id: 2,
          }),
          expect.objectContaining({
            attribute_type: 'allergen_may_contain',
            attribute_id: 3,
          }),
          expect.objectContaining({
            attribute_type: 'allergen_may_contain',
            attribute_id: 4,
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
    });
  });

  describe('addRecipeSteps', () => {
    const validStepsData = {
      recipe_id: 1,
      batch_number: 1,
      is_final_batch: false,
      steps: [
        {
          step_title: 'Prepare Ingredients',
          step_description: 'Gather all ingredients',
          step_note: 'Keep ingredients at room temperature',
          step_warning: 'Handle raw meat carefully',
          step_time: 5,
          step_temperature: null,
          step_temperature_unit: null,
        },
        {
          step_title: 'Cook Pasta',
          step_description: 'Boil water and cook pasta',
          step_note: 'Reserve pasta water',
          step_warning: 'Be careful with boiling water',
          step_time: 10,
          step_temperature: 100,
          step_temperature_unit: 'C',
        },
      ],
    };

    beforeEach(() => {
      const mockRecipe = {
        id: 1,
        recipe_title: 'Test Recipe',
        update: jest.fn().mockResolvedValue([1]),
      };
      (db.Recipe.findOne as jest.Mock).mockResolvedValue(mockRecipe);
    });

    it('should add recipe steps successfully', async () => {
      // Arrange
      mockReq.body = validStepsData;
      (db.RecipeSteps.update as jest.Mock).mockResolvedValue([1]);
      (db.RecipeSteps.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeSteps(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: true,
        message: 'Recipe steps batch 1 saved successfully',
        data: {
          recipe_id: 1,
          batch_number: 1,
          steps_in_batch: 2,
          is_final_batch: false,
        },
      });

      // Verify steps were processed correctly
      expect(db.RecipeSteps.update).toHaveBeenCalledWith(
        { status: 'inactive' },
        expect.objectContaining({
          where: { recipe_id: 1 },
          transaction: {},
        })
      );

      expect(db.RecipeSteps.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            recipe_id: 1,
            step_number: 1,
            step_title: 'Prepare Ingredients',
            step_description: 'Gather all ingredients',
            step_time: 5,
            status: 'active',
          }),
          expect.objectContaining({
            recipe_id: 1,
            step_number: 2,
            step_title: 'Cook Pasta',
            step_description: 'Boil water and cook pasta',
            step_time: 10,
            step_temperature: 100,
            step_temperature_unit: 'C',
            status: 'active',
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
    });

    it('should handle final batch correctly', async () => {
      // Arrange
      const finalBatchData = {
        ...validStepsData,
        batch_number: 2,
        is_final_batch: true,
      };
      mockReq.body = finalBatchData;
      (db.RecipeSteps.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeSteps(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        status: true,
        message: 'Recipe steps batch 2 saved successfully',
        data: {
          recipe_id: 1,
          batch_number: 2,
          steps_in_batch: 2,
          is_final_batch: true,
        },
      });

      // Verify history is created for final batch
      expect(createRecipeHistory).toHaveBeenCalledWith(
        expect.objectContaining({
          recipe_id: 1,
          action: 'updated',
          description: expect.stringContaining('steps were updated'),
        }),
        {}
      );
    });

    it('should calculate step numbers correctly for different batches', async () => {
      // Arrange - Batch 2 (steps 6-10)
      const batch2Data = {
        ...validStepsData,
        batch_number: 2,
        steps: [
          { step_description: 'Step 6' },
          { step_description: 'Step 7' },
          { step_description: 'Step 8' },
        ],
      };
      mockReq.body = batch2Data;
      (db.RecipeSteps.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeSteps(mockReq as Request, mockRes as Response);

      // Assert
      expect(db.RecipeSteps.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            step_number: 6, // (2-1) * 5 + 1
            step_description: 'Step 6',
          }),
          expect.objectContaining({
            step_number: 7, // (2-1) * 5 + 2
            step_description: 'Step 7',
          }),
          expect.objectContaining({
            step_number: 8, // (2-1) * 5 + 3
            step_description: 'Step 8',
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
    });

    it('should return 400 when steps is not an array', async () => {
      // Arrange
      const invalidData = {
        ...validStepsData,
        steps: 'not an array',
      };
      mockReq.body = invalidData;

      // Act
      await recipeBatchController.addRecipeSteps(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Steps must be an array',
      });
    });

    it('should not make existing steps inactive for non-first batch', async () => {
      // Arrange
      const batch2Data = {
        ...validStepsData,
        batch_number: 2,
      };
      mockReq.body = batch2Data;
      (db.RecipeSteps.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeSteps(mockReq as Request, mockRes as Response);

      // Assert
      expect(db.RecipeSteps.update).not.toHaveBeenCalled();
    });
  });

  describe('addRecipeUploads', () => {
    const mockFiles = [
      {
        fieldname: 'files',
        originalname: 'test-image.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('fake image data'),
        size: 1024,
        destination: '',
        filename: 'test-image.jpg',
        path: '',
        stream: {} as any,
      },
      {
        fieldname: 'files',
        originalname: 'test-document.pdf',
        encoding: '7bit',
        mimetype: 'application/pdf',
        buffer: Buffer.from('fake pdf data'),
        size: 2048,
        destination: '',
        filename: 'test-document.pdf',
        path: '',
        stream: {} as any,
      },
    ] as Express.Multer.File[];

    const validUploadData = {
      recipe_id: '1',
      batch_number: '1',
      is_final_batch: 'false',
    };

    beforeEach(() => {
      const mockRecipe = {
        id: 1,
        recipe_title: 'Test Recipe',
        update: jest.fn().mockResolvedValue([1]),
      };
      (db.Recipe.findOne as jest.Mock).mockResolvedValue(mockRecipe);

      // Mock upload service
      const uploadService = require('../../src/helper/upload.service').default;
      uploadService.uploadFileToBucket.mockResolvedValue({ success: true });
    });

    it('should upload recipe files successfully', async () => {
      // Arrange
      mockReq.body = validUploadData;
      mockReq.files = mockFiles;
      (db.RecipeResources.update as jest.Mock).mockResolvedValue([1]);
      (db.RecipeResources.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeUploads(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: true,
        message: 'Recipe uploads batch 1 saved successfully',
        data: {
          recipe_id: 1,
          batch_number: 1,
          files_uploaded: 2,
          is_final_batch: false,
        },
      });

      // Verify files were uploaded
      const uploadService = require('../../src/helper/upload.service').default;
      expect(uploadService.uploadFileToBucket).toHaveBeenCalledTimes(2);

      // Verify first file upload
      expect(uploadService.uploadFileToBucket).toHaveBeenCalledWith(
        'test', // bucket name from env
        expect.stringContaining('1/recipe_files/1/'), // file path
        mockFiles[0].buffer,
        'image/jpeg'
      );

      // Verify resources were created
      expect(db.RecipeResources.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            recipe_id: 1,
            resource_title: 'test-image.jpg',
            resource_type: 'image',
            resource_size: 1024,
            resource_order: 1,
            status: 'active',
          }),
          expect.objectContaining({
            recipe_id: 1,
            resource_title: 'test-document.pdf',
            resource_type: 'document',
            resource_size: 2048,
            resource_order: 2,
            status: 'active',
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
    });

    it('should handle final batch correctly', async () => {
      // Arrange
      const finalBatchData = {
        ...validUploadData,
        batch_number: '2',
        is_final_batch: 'true',
      };
      mockReq.body = finalBatchData;
      mockReq.files = [mockFiles[0]] as Express.Multer.File[]; // Single file
      (db.RecipeResources.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeUploads(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.json).toHaveBeenCalledWith({
        status: true,
        message: 'Recipe uploads batch 2 saved successfully',
        data: {
          recipe_id: 1,
          batch_number: 2,
          files_uploaded: 1,
          is_final_batch: true,
        },
      });

      // Verify history is created for final batch
      expect(createRecipeHistory).toHaveBeenCalledWith(
        expect.objectContaining({
          recipe_id: 1,
          action: 'updated',
          description: expect.stringContaining('resources were updated'),
        }),
        {}
      );
    });

    it('should calculate resource order correctly for different batches', async () => {
      // Arrange - Batch 2 (resources 6-10)
      const batch2Data = {
        ...validUploadData,
        batch_number: '2',
      };
      mockReq.body = batch2Data;
      mockReq.files = [mockFiles[0]] as Express.Multer.File[];
      (db.RecipeResources.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeUploads(mockReq as Request, mockRes as Response);

      // Assert
      expect(db.RecipeResources.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            resource_order: 6, // (2-1) * 5 + 1
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
    });

    it('should return 400 when no files are uploaded', async () => {
      // Arrange
      mockReq.body = validUploadData;
      mockReq.files = [];

      // Act
      await recipeBatchController.addRecipeUploads(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(StatusCodes.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'No files uploaded',
      });
    });

    it('should handle upload service failure', async () => {
      // Arrange
      mockReq.body = validUploadData;
      mockReq.files = [mockFiles[0]] as Express.Multer.File[];

      const uploadService = require('../../src/helper/upload.service').default;
      uploadService.uploadFileToBucket.mockResolvedValue({ success: false });

      // Act
      await recipeBatchController.addRecipeUploads(mockReq as Request, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: false,
        message: 'Error uploading recipe files',
        error: expect.stringContaining('Failed to upload file'),
      });
    });

    it('should determine file type correctly', async () => {
      // Arrange
      const imageFile = {
        ...mockFiles[0],
        mimetype: 'image/png',
      };
      const documentFile = {
        ...mockFiles[1],
        mimetype: 'application/pdf',
      };

      mockReq.body = validUploadData;
      mockReq.files = [imageFile, documentFile] as Express.Multer.File[];
      (db.RecipeResources.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeUploads(mockReq as Request, mockRes as Response);

      // Assert
      expect(db.RecipeResources.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            resource_type: 'image',
          }),
          expect.objectContaining({
            resource_type: 'document',
          }),
        ]),
        expect.objectContaining({ transaction: {} })
      );
    });

    it('should not make existing resources inactive for non-first batch', async () => {
      // Arrange
      const batch2Data = {
        ...validUploadData,
        batch_number: '2',
      };
      mockReq.body = batch2Data;
      mockReq.files = [mockFiles[0]] as Express.Multer.File[];
      (db.RecipeResources.bulkCreate as jest.Mock).mockResolvedValue([]);

      // Act
      await recipeBatchController.addRecipeUploads(mockReq as Request, mockRes as Response);

      // Assert
      expect(db.RecipeResources.update).not.toHaveBeenCalled();
    });
  });
});
