/**
 * Unit tests for Batch Helper Functions
 */

import {
  calculateStepBatches,
  splitIntoBatches,
  generateBatchProgress,
} from '../../src/helper/batch.helper';

describe('Batch Helper Functions', () => {
  describe('calculateStepBatches', () => {
    it('should calculate batches correctly with default batch size', () => {
      // Test cases with default batch size of 5
      expect(calculateStepBatches(8)).toEqual({
        totalBatches: 2,
        batchSize: 5,
        totalSteps: 8,
      });

      expect(calculateStepBatches(5)).toEqual({
        totalBatches: 1,
        batchSize: 5,
        totalSteps: 5,
      });

      expect(calculateStepBatches(12)).toEqual({
        totalBatches: 3,
        batchSize: 5,
        totalSteps: 12,
      });
    });

    it('should calculate batches correctly with custom batch size', () => {
      expect(calculateStepBatches(10, 3)).toEqual({
        totalBatches: 4,
        batchSize: 3,
        totalSteps: 10,
      });

      expect(calculateStepBatches(6, 2)).toEqual({
        totalBatches: 3,
        batchSize: 2,
        totalSteps: 6,
      });
    });

    it('should handle edge cases', () => {
      // Zero steps
      expect(calculateStepBatches(0)).toEqual({
        totalBatches: 0,
        batchSize: 5,
        totalSteps: 0,
      });

      // One step
      expect(calculateStepBatches(1)).toEqual({
        totalBatches: 1,
        batchSize: 5,
        totalSteps: 1,
      });
    });
  });

  describe('splitIntoBatches', () => {
    it('should split array into batches correctly with default size', () => {
      const items = [1, 2, 3, 4, 5, 6, 7, 8];
      const result = splitIntoBatches(items);

      expect(result).toEqual([
        [1, 2, 3, 4, 5],
        [6, 7, 8],
      ]);
    });

    it('should split array into batches correctly with custom size', () => {
      const items = ['a', 'b', 'c', 'd', 'e', 'f', 'g'];
      const result = splitIntoBatches(items, 3);

      expect(result).toEqual([
        ['a', 'b', 'c'],
        ['d', 'e', 'f'],
        ['g'],
      ]);
    });

    it('should handle arrays smaller than batch size', () => {
      const items = [1, 2, 3];
      const result = splitIntoBatches(items, 5);

      expect(result).toEqual([[1, 2, 3]]);
    });

    it('should handle empty arrays', () => {
      const items: number[] = [];
      const result = splitIntoBatches(items);

      expect(result).toEqual([]);
    });

    it('should handle arrays with exact batch size multiples', () => {
      const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const result = splitIntoBatches(items, 5);

      expect(result).toEqual([
        [1, 2, 3, 4, 5],
        [6, 7, 8, 9, 10],
      ]);
    });

    it('should work with different data types', () => {
      const items = [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
        { id: 3, name: 'Item 3' },
        { id: 4, name: 'Item 4' },
      ];
      const result = splitIntoBatches(items, 2);

      expect(result).toEqual([
        [
          { id: 1, name: 'Item 1' },
          { id: 2, name: 'Item 2' },
        ],
        [
          { id: 3, name: 'Item 3' },
          { id: 4, name: 'Item 4' },
        ],
      ]);
    });
  });

  describe('generateBatchProgress', () => {
    it('should generate correct progress for first batch', () => {
      const result = generateBatchProgress(1, 3, 5, 12);

      expect(result).toEqual({
        currentBatch: 1,
        totalBatches: 3,
        itemsInBatch: 5,
        totalItems: 12,
        percentComplete: 33, // Math.round((1/3) * 100)
        isComplete: false,
      });
    });

    it('should generate correct progress for middle batch', () => {
      const result = generateBatchProgress(2, 4, 3, 15);

      expect(result).toEqual({
        currentBatch: 2,
        totalBatches: 4,
        itemsInBatch: 3,
        totalItems: 15,
        percentComplete: 50, // Math.round((2/4) * 100)
        isComplete: false,
      });
    });

    it('should generate correct progress for final batch', () => {
      const result = generateBatchProgress(3, 3, 2, 12);

      expect(result).toEqual({
        currentBatch: 3,
        totalBatches: 3,
        itemsInBatch: 2,
        totalItems: 12,
        percentComplete: 100, // Math.round((3/3) * 100)
        isComplete: true,
      });
    });

    it('should handle single batch scenario', () => {
      const result = generateBatchProgress(1, 1, 8, 8);

      expect(result).toEqual({
        currentBatch: 1,
        totalBatches: 1,
        itemsInBatch: 8,
        totalItems: 8,
        percentComplete: 100,
        isComplete: true,
      });
    });

    it('should handle edge cases with zero values', () => {
      const result = generateBatchProgress(0, 0, 0, 0);

      expect(result).toEqual({
        currentBatch: 0,
        totalBatches: 0,
        itemsInBatch: 0,
        totalItems: 0,
        percentComplete: NaN, // 0/0 = NaN
        isComplete: true, // 0 === 0 is true
      });
    });

    it('should calculate percentage correctly for various scenarios', () => {
      // Test rounding
      expect(generateBatchProgress(1, 3, 5, 15).percentComplete).toBe(33);
      expect(generateBatchProgress(2, 3, 5, 15).percentComplete).toBe(67);
      expect(generateBatchProgress(3, 3, 5, 15).percentComplete).toBe(100);

      // Test with 7 batches
      expect(generateBatchProgress(1, 7, 2, 14).percentComplete).toBe(14);
      expect(generateBatchProgress(4, 7, 2, 14).percentComplete).toBe(57);
    });
  });

  describe('Integration scenarios', () => {
    it('should work together for recipe steps processing', () => {
      const totalSteps = 13;
      const batchSize = 5;

      // Calculate batches
      const batchInfo = calculateStepBatches(totalSteps, batchSize);
      expect(batchInfo.totalBatches).toBe(3);

      // Create steps array
      const steps = Array.from({ length: totalSteps }, (_, i) => ({
        id: i + 1,
        description: `Step ${i + 1}`,
      }));

      // Split into batches
      const batches = splitIntoBatches(steps, batchSize);
      expect(batches).toHaveLength(3);
      expect(batches[0]).toHaveLength(5);
      expect(batches[1]).toHaveLength(5);
      expect(batches[2]).toHaveLength(3);

      // Generate progress for each batch
      const progress1 = generateBatchProgress(1, 3, batches[0].length, totalSteps);
      const progress2 = generateBatchProgress(2, 3, batches[1].length, totalSteps);
      const progress3 = generateBatchProgress(3, 3, batches[2].length, totalSteps);

      expect(progress1.percentComplete).toBe(33);
      expect(progress1.isComplete).toBe(false);

      expect(progress2.percentComplete).toBe(67);
      expect(progress2.isComplete).toBe(false);

      expect(progress3.percentComplete).toBe(100);
      expect(progress3.isComplete).toBe(true);
    });

    it('should work together for file upload processing', () => {
      const totalFiles = 8;
      const batchSize = 3;

      // Calculate batches
      const batchInfo = calculateStepBatches(totalFiles, batchSize);
      expect(batchInfo.totalBatches).toBe(3);

      // Create files array
      const files = Array.from({ length: totalFiles }, (_, i) => ({
        name: `file${i + 1}.jpg`,
        size: 1024 * (i + 1),
      }));

      // Split into batches
      const batches = splitIntoBatches(files, batchSize);
      expect(batches).toHaveLength(3);

      // Verify each batch
      batches.forEach((batch, index) => {
        const progress = generateBatchProgress(
          index + 1,
          batches.length,
          batch.length,
          totalFiles
        );

        if (index === batches.length - 1) {
          expect(progress.isComplete).toBe(true);
        } else {
          expect(progress.isComplete).toBe(false);
        }
      });
    });
  });
});
